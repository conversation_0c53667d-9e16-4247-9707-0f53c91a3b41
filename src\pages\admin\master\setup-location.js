const { ipc<PERSON>ender<PERSON> } = require('electron');

// Sidebar functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

function handleNavClick(page) {
    switch (page) {
        case 'dashboard':
            ipcRenderer.invoke('navigate-to-admin-section', 'dashboard');
            break;
        case 'master':
            // Toggle master dropdown
            const masterSubitems = document.getElementById('master-subitems');
            masterSubitems.classList.toggle('hidden');
            break;
        case 'setup-location':
            // Already on this page
            break;
        case 'setup-category':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-category.html');
            break;
        case 'setup-supplier':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-supplier.html');
            break;
        case 'setup-product':
            ipcRenderer.invoke('navigate-to-page', 'admin/master/setup-product.html');
            break;
        case 'reports':
            ipcRenderer.invoke('navigate-to-admin-section', 'reports');
            break;
        case 'transactions':
            ipcRenderer.invoke('navigate-to-admin-section', 'transactions');
            break;
        case 'wholesale':
            ipcRenderer.invoke('navigate-to-admin-section', 'wholesale');
            break;
        case 'user-management':
            ipcRenderer.invoke('navigate-to-admin-section', 'user-management');
            break;
        case 'back-to-pos':
            ipcRenderer.invoke('navigate-to', 'pos');
            break;
        default:
            console.log('Unknown navigation:', page);
    }
}

// Locations data - loaded from database
let locations = [];
let isEditMode = false;
let currentUser = null;

// Form data object
let formData = {
    locationCode: "",
    location: "",
    companyName: "",
    address1: "",
    address2: "",
    phone: "",
    taxPercent: "",
    email: "",
    appMode: "",
    theaterPLU: "",
    theaterTime: "",
    deli: false
};

// Message display function
function showMessage(message, type = 'info') {
    // Remove existing message
    const existingMessage = document.querySelector('.message-display');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-display message-${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease-out;
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            messageDiv.style.backgroundColor = '#10b981';
            break;
        case 'error':
            messageDiv.style.backgroundColor = '#ef4444';
            break;
        case 'info':
            messageDiv.style.backgroundColor = '#3b82f6';
            break;
        default:
            messageDiv.style.backgroundColor = '#6b7280';
    }

    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => messageDiv.remove(), 300);
        }
    }, 4000);
}

// Add CSS animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    // Load current user information
    await loadCurrentUser();

    updateTime();
    setInterval(updateTime, 1000);

    // Load locations from database
    await loadLocations();

    // Initialize maximize button state
    try {
        const isMaximized = await ipcRenderer.invoke('is-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error checking initial maximize state:', error);
    }
});

// Time functions
function updateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = formatDate(now);
    document.getElementById('current-time').textContent = formatTime(now);
}

function formatDate(date) {
    return date.toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "2-digit" });
}

function formatTime(date) {
    return date.toLocaleTimeString("en-US", { hour12: false });
}

// Current user management
async function loadCurrentUser() {
    try {
        console.log('Setup Location - Loading current user...');
        currentUser = await ipcRenderer.invoke('get-current-user');

        if (currentUser) {
            console.log('Setup Location - User loaded:', {
                username: currentUser.username,
                role: currentUser.role,
                location: currentUser.location_name
            });

            updateOperatorInfo();
        } else {
            console.error('Setup Location - No current user returned');
            document.getElementById('current-operator').textContent = 'Error: No User';
        }
    } catch (error) {
        console.error('Error loading current user:', error);
        document.getElementById('current-operator').textContent = 'Error: User Load Failed';
    }
}

function updateOperatorInfo() {
    if (currentUser) {
        const operatorSpan = document.getElementById('current-operator');
        if (operatorSpan) {
            operatorSpan.textContent = currentUser.name || currentUser.username || 'Unknown';
        }

        // Update location name in header
        const locationNameElement = document.getElementById('location-name');
        if (locationNameElement) {
            if (currentUser.location_name) {
                locationNameElement.textContent = currentUser.location_name;
            } else {
                locationNameElement.textContent = 'Rainbow Station Inc.';
            }
        }
    }
}

// Navigation functions
function navigateToAdmin() {
    ipcRenderer.invoke('navigate-to', 'admin');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

async function toggleFullscreen() {
    try {
        const isMaximized = await ipcRenderer.invoke('toggle-fullscreen');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isMaximized ? '🗗' : '⛶';
            fullscreenBtn.title = isMaximized ? 'Restore Window' : 'Maximize Window';
        }
    } catch (error) {
        console.error('Error toggling maximize:', error);
    }
}

// Form functions
function getFormData() {
    return {
        locationCode: document.getElementById('locationCode').value,
        location: document.getElementById('location').value,
        companyName: document.getElementById('companyName').value,
        address1: document.getElementById('address1').value,
        address2: document.getElementById('address2').value,
        phone: document.getElementById('phone').value,
        taxPercent: document.getElementById('taxPercent').value,
        email: document.getElementById('email').value,
        appMode: document.getElementById('appMode').value,
        theaterPLU: document.getElementById('theaterPLU').value,
        theaterTime: document.getElementById('theaterTime').value,
        deli: document.getElementById('deli').checked
    };
}

function setFormData(data) {
    document.getElementById('locationCode').value = data.locationCode || '';
    document.getElementById('location').value = data.location || '';
    document.getElementById('companyName').value = data.companyName || '';
    document.getElementById('address1').value = data.address1 || '';
    document.getElementById('address2').value = data.address2 || '';
    document.getElementById('phone').value = data.phone || '';
    document.getElementById('taxPercent').value = data.taxPercent || '';
    document.getElementById('email').value = data.email || '';
    document.getElementById('appMode').value = data.appMode || '';
    document.getElementById('theaterPLU').value = data.theaterPLU || '';
    document.getElementById('theaterTime').value = data.theaterTime || '';
    document.getElementById('deli').checked = data.deli || false;
}

async function saveLocation() {
    const data = getFormData();

    if (!data.locationCode || !data.location) {
        showMessage('Please fill in required fields: Location Code and Location', 'error');
        return;
    }

    try {
        // Prepare location data for database
        const locationData = {
            location_code: data.locationCode,
            location: data.location,
            company_name: data.companyName,
            address1: data.address1,
            address2: data.address2,
            phone: data.phone,
            tax_percent: parseFloat(data.taxPercent) || 0,
            email: data.email,
            app_mode: data.appMode,
            theater_plu: data.theaterPLU,
            theater_time: data.theaterTime,
            deli: data.deli,
            status: 'active'
        };

        let result;
        if (isEditMode) {
            // Update existing location
            const existingLocation = await ipcRenderer.invoke('get-location-by-code', data.locationCode);
            if (existingLocation.success && existingLocation.location) {
                result = await ipcRenderer.invoke('update-location', existingLocation.location.id, locationData);
                if (result.success) {
                    showMessage('Location updated successfully!', 'success');
                    exitEditMode();
                } else {
                    showMessage('Error updating location: ' + result.message, 'error');
                }
            } else {
                showMessage('Location not found for update!', 'error');
                return;
            }
        } else {
            // Check if location already exists for new creation
            const existingLocation = await ipcRenderer.invoke('get-location-by-code', data.locationCode);
            if (existingLocation.success && existingLocation.location) {
                showMessage('Location with this code already exists!', 'error');
                return;
            }

            // Create new location
            result = await ipcRenderer.invoke('create-location', locationData);
            if (result.success) {
                showMessage('Location created successfully!', 'success');
            } else {
                showMessage('Error creating location: ' + result.message, 'error');
            }
        }

        if (result.success) {
            await loadLocations();
            clearForm();
        }

        console.log('Location operation result:', result);
    } catch (error) {
        console.error('Error saving location:', error);
        showMessage('Error saving location: ' + error.message, 'error');
    }
}

// Exit edit mode
function exitEditMode() {
    isEditMode = false;
    const saveBtn = document.querySelector('.btn-green');
    if (saveBtn) {
        saveBtn.textContent = 'SAVE';
        saveBtn.style.backgroundColor = '#10b981';
    }
}

function clearForm() {
    setFormData({});
    exitEditMode(); // Exit edit mode when clearing form
    console.log('Form cleared');
}

async function deleteLocation() {
    const locationCode = document.getElementById('locationCode').value;

    if (!locationCode) {
        alert('Please select a location to delete');
        return;
    }

    try {
        // Find the location by code
        const existingLocation = await ipcRenderer.invoke('get-location-by-code', locationCode);

        if (!existingLocation.success || !existingLocation.location) {
            alert('Location not found: ' + locationCode);
            return;
        }

        if (confirm('Are you sure you want to delete this location?\n\nCode: ' + locationCode + '\nLocation: ' + existingLocation.location.location)) {
            const result = await ipcRenderer.invoke('delete-location', existingLocation.location.id);

            if (result.success) {
                alert('Location deleted successfully!');
                await loadLocations();
                clearForm();
            } else {
                alert('Error deleting location: ' + result.message);
            }
        }
    } catch (error) {
        console.error('Error deleting location:', error);
        alert('Error deleting location: ' + error.message);
    }
}

async function loadLocations() {
    try {
        const result = await ipcRenderer.invoke('get-all-locations');

        if (result && result.success) {
            locations = result.locations.map(loc => ({
                locationCode: loc.location_code,
                location: loc.location,
                companyName: loc.company_name || '',
                address1: loc.address1 || '',
                address2: loc.address2 || '',
                phone: loc.phone || '',
                taxPercent: loc.tax_percent || '0',
                id: loc.id,
                fullData: loc
            }));

            renderLocationTable();
        } else {
            console.error('Error loading locations:', result);
        }
    } catch (error) {
        console.error('Error loading locations:', error);
    }
}

function selectLocation(locationCode) {
    const location = locations.find(loc => loc.locationCode === locationCode);
    if (location && location.fullData) {
        const fullData = location.fullData;
        setFormData({
            locationCode: fullData.location_code,
            location: fullData.location,
            companyName: fullData.company_name,
            address1: fullData.address1,
            address2: fullData.address2,
            phone: fullData.phone,
            taxPercent: fullData.tax_percent,
            email: fullData.email,
            appMode: fullData.app_mode,
            theaterPLU: fullData.theater_plu,
            theaterTime: fullData.theater_time,
            deli: fullData.deli === 1
        });
    }
}

function renderLocationTable() {
    const tbody = document.getElementById('locationTableBody');

    if (!tbody) {
        console.error('locationTableBody element not found!');
        return;
    }

    tbody.innerHTML = '';

    locations.forEach((location, index) => {
        const row = document.createElement('tr');
        row.style.cursor = 'pointer';

        row.innerHTML = `
            <td class="font-medium">${location.locationCode}</td>
            <td>${location.location}</td>
            <td>${location.companyName}</td>
            <td>${location.address1}</td>
            <td>${location.address2}</td>
            <td class="text-center">${location.phone}</td>
            <td class="text-center">${location.taxPercent}%</td>
            <td class="text-center">
                <div class="flex gap-2 justify-center">
                    <button class="btn btn-blue btn-small" onclick="editLocation('${location.locationCode}')" title="Edit Location">
                        Edit
                    </button>
                    <button class="btn btn-red btn-small" onclick="confirmDeleteLocation('${location.locationCode}')" title="Delete Location">
                        Delete
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// Edit location function
function editLocation(locationCode) {
    const location = locations.find(loc => loc.locationCode === locationCode);
    if (location) {
        // Set edit mode
        isEditMode = true;

        // Load location data into form
        setFormData({
            locationCode: location.locationCode,
            location: location.location,
            companyName: location.companyName,
            address1: location.address1,
            address2: location.address2,
            phone: location.phone,
            email: location.email || '',
            theaterPLU: location.theaterPLU || '',
            theaterTime: location.theaterTime || '',
            taxPercent: location.taxPercent,
            appMode: location.appMode || '',
            deli: location.deli || false
        });

        // Change save button to update mode
        const saveBtn = document.querySelector('.btn-green');
        if (saveBtn) {
            saveBtn.textContent = 'UPDATE';
            saveBtn.style.backgroundColor = '#f59e0b';
        }

        showMessage(`Editing location: ${location.location}`, 'info');

        // Scroll to form
        document.querySelector('.form-card').scrollIntoView({ behavior: 'smooth' });
    }
}

// Confirm delete function
function confirmDeleteLocation(locationCode) {
    const location = locations.find(loc => loc.locationCode === locationCode);
    if (location && confirm(`Are you sure you want to delete location "${location.location}"?`)) {
        deleteLocationByCode(locationCode);
    }
}

// Delete location by code
async function deleteLocationByCode(locationCode) {
    try {
        const existingLocation = await ipcRenderer.invoke('get-location-by-code', locationCode);

        if (existingLocation.success && existingLocation.location) {
            const result = await ipcRenderer.invoke('delete-location', existingLocation.location.id);

            if (result.success) {
                showMessage('Location deleted successfully!', 'success');
                await loadLocations();
                clearForm();
            } else {
                showMessage('Error deleting location: ' + result.message, 'error');
            }
        } else {
            showMessage('Location not found!', 'error');
        }
    } catch (error) {
        console.error('Error deleting location:', error);
        showMessage('Error deleting location: ' + error.message, 'error');
    }
}
