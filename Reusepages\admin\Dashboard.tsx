export default function Dashboard() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Dashboard Overview</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white border-2 border-green-400 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-bold text-green-600 mb-2">Today's Sales</h3>
          <p className="text-3xl font-black text-gray-800">$2,450.75</p>
        </div>
        <div className="bg-white border-2 border-blue-400 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-bold text-blue-600 mb-2">Transactions</h3>
          <p className="text-3xl font-black text-gray-800">127</p>
        </div>
        <div className="bg-white border-2 border-yellow-400 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-bold text-yellow-600 mb-2">Products Sold</h3>
          <p className="text-3xl font-black text-gray-800">342</p>
        </div>
        <div className="bg-white border-2 border-red-400 rounded-lg p-6 shadow-md">
          <h3 className="text-lg font-bold text-red-600 mb-2">Active Users</h3>
          <p className="text-3xl font-black text-gray-800">8</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
              <span className="text-green-700 font-medium">Sale completed - $45.99</span>
              <span className="text-gray-500 text-sm">2 minutes ago</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
              <span className="text-blue-700 font-medium">New user registered - John Doe</span>
              <span className="text-gray-500 text-sm">5 minutes ago</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
              <span className="text-yellow-700 font-medium">Inventory updated - 25 items</span>
              <span className="text-gray-500 text-sm">10 minutes ago</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-purple-50 border border-purple-200 rounded">
              <span className="text-purple-700 font-medium">Report generated - Monthly Sales</span>
              <span className="text-gray-500 text-sm">15 minutes ago</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <button className="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
              New Sale
            </button>
            <button className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
              Add Product
            </button>
            <button className="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
              View Reports
            </button>
            <button className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
              Manage Users
            </button>
          </div>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Sales Chart</h3>
        <div className="h-64 bg-gray-50 border border-gray-200 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl text-gray-400 mb-2">📊</div>
            <p className="text-gray-600">Sales chart visualization would go here</p>
            <p className="text-sm text-gray-500">Integration with charting library needed</p>
          </div>
        </div>
      </div>
    </div>
  )
}
