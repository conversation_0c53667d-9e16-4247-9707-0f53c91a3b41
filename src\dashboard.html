<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Rainbow Station Inc POS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Arial', sans-serif;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .dashboard-container {
            text-align: center;
            padding: 50px;
            border: 2px solid #00ff00;
            border-radius: 10px;
            background-color: #111111;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }

        .welcome-message {
            font-size: 48px;
            color: #00ff00;
            text-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
            }
            to {
                text-shadow: 0 0 25px rgba(0, 255, 0, 1);
            }
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #00ff00;
            font-size: 20px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .close-button:hover {
            color: #ff0000;
        }
    </style>
</head>
<body>
    <button class="close-button" onclick="closeApp()">×</button>
    
    <div class="dashboard-container">
        <h1 class="welcome-message">Welcome to Rainbow Station Inc POS System</h1>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        function closeApp() {
            ipcRenderer.invoke('close-app');
        }
    </script>
</body>
</html>
