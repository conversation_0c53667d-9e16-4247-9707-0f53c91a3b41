const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Track if login is already initialized to prevent duplicates
let loginInitialized = false;
let initializationTimeout = null;

// Initialize login functionality when DOM is ready
function initializeLogin() {
    console.log('Initializing login...');

    // Clear any pending initialization
    if (initializationTimeout) {
        clearTimeout(initializationTimeout);
        initializationTimeout = null;
    }

    const loginForm = document.getElementById('loginForm');
    if (!loginForm) {
        console.error('Login form not found');
        return;
    }

    // Prevent duplicate initialization
    if (loginInitialized) {
        console.log('Login already initialized, just resetting form');
        resetForm();
        enableFormInputs();
        return;
    }

    console.log('Setting up login form event listeners');
    loginInitialized = true;

    loginForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const role = document.getElementById('role').value;
    const errorDiv = document.getElementById('errorMessage');
    const loginButton = document.querySelector('.login-button');

    // Clear previous error
    errorDiv.style.display = 'none';
    errorDiv.textContent = '';

    // Validate inputs
    if (!username || !password || !role) {
        showError('Please fill in all fields');
        return;
    }

    // Allow all roles: Admin, Cashier, CCTV
    const allowedRoles = ['Admin', 'Cashier', 'CCTV'];
    if (!allowedRoles.includes(role)) {
        showError('Invalid role selected');
        return;
    }

    // Show loading state
    loginButton.textContent = 'Logging in...';
    loginButton.disabled = true;

    try {
        // Send login request to main process
        const result = await ipcRenderer.invoke('login', {
            username: username,
            password: password,
            role: role
        });

        if (result.success) {
            // Login successful - main process will handle redirect
            loginButton.textContent = 'Success!';
            console.log('Login successful');
        } else {
            showError(result.message || 'Invalid credentials');
            resetLoginButton();
        }
    } catch (error) {
        showError('An error occurred during login');
        console.error('Login error:', error);
        resetLoginButton();
    }
    });

    // Handle Enter key in form fields
    document.querySelectorAll('input, select').forEach(element => {
        element.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    });

    // Reset form to clean state
    resetForm();
}

function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
}

function resetLoginButton() {
    const loginButton = document.querySelector('.login-button');
    if (loginButton) {
        loginButton.textContent = 'Login';
        loginButton.disabled = false;
    }
}

// Reset form to initial state
function resetForm() {
    console.log('Resetting login form...');
    const form = document.getElementById('loginForm');
    if (form) {
        // Clear all form fields manually
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        const roleField = document.getElementById('role');

        console.log('Form fields found:', {
            username: !!usernameField,
            password: !!passwordField,
            role: !!roleField
        });

        if (usernameField) {
            usernameField.value = '';
            usernameField.disabled = false;
            usernameField.removeAttribute('readonly');
            usernameField.removeAttribute('disabled');
            usernameField.style.backgroundColor = '';
            usernameField.style.cursor = '';
        }

        if (passwordField) {
            passwordField.value = '';
            passwordField.disabled = false;
            passwordField.removeAttribute('readonly');
            passwordField.removeAttribute('disabled');
            passwordField.style.backgroundColor = '';
            passwordField.style.cursor = '';
        }

        if (roleField) {
            roleField.value = '';
            roleField.disabled = false;
            roleField.removeAttribute('disabled');
            roleField.style.backgroundColor = '';
            roleField.style.cursor = '';
        }

        resetLoginButton();

        // Clear error message
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.style.display = 'none';
            errorDiv.textContent = '';
        }

        // Focus username field immediately
        if (usernameField) {
            usernameField.focus();
            usernameField.click(); // Also trigger click to ensure focus
        }
    }
}

// Force enable all form inputs with comprehensive reset
function enableFormInputs() {
    console.log('Force enabling form inputs...');

    // Get all form elements
    const form = document.getElementById('loginForm');
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    const roleField = document.getElementById('role');
    const loginButton = document.querySelector('.login-button');

    // Enable form container immediately
    if (form) {
        form.style.pointerEvents = 'auto';
        form.style.opacity = '1';
        form.style.userSelect = 'auto';
        form.style.webkitUserSelect = 'auto';
        form.removeAttribute('disabled');
        form.removeAttribute('readonly');
    }

    // Enable all inputs specifically with comprehensive reset
    [usernameField, passwordField, roleField, loginButton].forEach(element => {
        if (element) {
            // Remove all blocking attributes
            element.disabled = false;
            element.removeAttribute('readonly');
            element.removeAttribute('disabled');
            element.removeAttribute('tabindex');
            element.tabIndex = 0; // Ensure proper tab order

            // Reset all blocking styles with !important to override any conflicts
            element.style.setProperty('pointer-events', 'auto', 'important');
            element.style.setProperty('opacity', '1', 'important');
            element.style.setProperty('user-select', 'auto', 'important');
            element.style.setProperty('-webkit-user-select', 'auto', 'important');
            element.style.setProperty('-moz-user-select', 'auto', 'important');
            element.style.setProperty('-ms-user-select', 'auto', 'important');
            element.style.setProperty('z-index', 'auto', 'important');
            element.style.removeProperty('position');
            element.style.removeProperty('background-color');

            // Ensure element is interactive
            if (element.type !== 'button' && element.type !== 'submit') {
                element.style.setProperty('cursor', 'text', 'important');
            } else {
                element.style.setProperty('cursor', 'pointer', 'important');
            }
        }
    });

    // Enable all inputs and buttons globally with force
    const allInputs = document.querySelectorAll('input, select, button');
    allInputs.forEach(input => {
        input.disabled = false;
        input.removeAttribute('readonly');
        input.removeAttribute('disabled');
        input.style.setProperty('pointer-events', 'auto', 'important');
        input.style.setProperty('opacity', '1', 'important');
        input.style.setProperty('user-select', 'auto', 'important');
    });

    console.log('Form inputs enabled successfully');
}

// Window controls
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

// Robust initialization function that handles all scenarios
function performCompleteInitialization() {
    console.log('Performing complete login initialization...');

    // Clear any existing timeout
    if (initializationTimeout) {
        clearTimeout(initializationTimeout);
        initializationTimeout = null;
    }

    // Reset initialization flag to allow fresh setup
    loginInitialized = false;

    // Perform initialization in correct order
    initializeLogin();
    enableFormInputs();
    resetForm();

    // Focus username field after a brief delay to ensure DOM is ready
    setTimeout(() => {
        const usernameField = document.getElementById('username');
        if (usernameField) {
            usernameField.focus();
            usernameField.select();
        }
    }, 50);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded - initializing login');
    performCompleteInitialization();
});

// Force re-initialization for logout scenarios
function forceReinitialize() {
    console.log('Force reinitializing login form...');

    // Use timeout to avoid conflicts with multiple rapid calls
    if (initializationTimeout) {
        clearTimeout(initializationTimeout);
    }

    initializationTimeout = setTimeout(() => {
        if (document.getElementById('loginForm')) {
            performCompleteInitialization();
        }
    }, 100);
}

// Listen for IPC messages to reset form (from logout)
ipcRenderer.on('reset-login-form', () => {
    console.log('Received reset-login-form message');
    forceReinitialize();
});

ipcRenderer.on('force-form-reset', () => {
    console.log('Received force-form-reset message');
    forceReinitialize();
});

ipcRenderer.on('enable-form-inputs', () => {
    console.log('Received enable-form-inputs message');
    forceReinitialize();
});
