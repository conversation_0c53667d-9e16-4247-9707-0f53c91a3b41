# New Reorder Level Checking System

## Overview

This document describes the implementation of the new reorder level checking method that simplifies stock management by using the `location_stocks` table as the single source of truth for reorder level calculations.

## Key Changes

### 1. Database Schema Updates

**Added `min_qty` column to `location_stocks` table:**
```sql
ALTER TABLE location_stocks ADD COLUMN min_qty INTEGER DEFAULT 0;
```

**Migration Logic:**
- Automatically migrates existing data by copying `min_qty` from `products` table to `location_stocks` table
- Handles both new installations and existing databases

### 2. Product Setup Process

**Automatic Stock Assignment:**
- When creating a new product, `max_qty` is automatically assigned as the initial stock in `location_stocks.stock`
- `min_qty` is automatically assigned to `location_stocks.min_qty`
- This ensures consistent initial setup across all locations

**Modified Functions:**
- `createLocationStocks()` - Now accepts and uses min_qty and max_qty parameters
- `updateLocationStocks()` - Updated to handle min_qty column
- Product setup form automatically sets initial stock to max_qty for new products

### 3. Reorder Level Logic

**New Checking Method:**
- Reorder level checking now uses `location_stocks.min_qty` instead of `products.min_qty`
- Query changed from `LEFT JOIN` to `INNER JOIN` to ensure only products with location stocks are checked
- Condition changed from `p.min_qty > 0` to `COALESCE(ls.min_qty, 0) > 0`

**Updated SQL Query:**
```sql
SELECT
    p.id, p.barcode, p.description, p.category, p.subcategory, p.supplier,
    p.min_qty as product_min_qty, p.max_qty,
    ls.location,
    COALESCE(ls.stock, 0) as stock,
    COALESCE(ls.min_qty, 0) as min_qty,
    COALESCE(ls.price, 0) as price,
    CASE
        WHEN COALESCE(ls.stock, 0) <= COALESCE(ls.min_qty, 0) THEN 'critical'
        WHEN COALESCE(ls.stock, 0) <= (COALESCE(ls.min_qty, 0) + threshold) THEN 'low'
        ELSE 'normal'
    END as stock_status
FROM products p
INNER JOIN location_stocks ls ON p.id = ls.product_id
WHERE COALESCE(ls.min_qty, 0) > 0
AND (COALESCE(ls.stock, 0) <= COALESCE(ls.min_qty, 0) OR ...)
```

### 4. Sales Integration

**Stock Reduction:**
- Sales continue to reduce stock in `location_stocks.stock`
- Reorder alerts now check against `location_stocks.min_qty`
- Automatic reorder level checking after each sale

**Updated Functions:**
- `reduceStock()` - Now uses `location_stocks.min_qty` for reorder alerts
- `updateInventoryAfterSale()` - Unchanged, continues to work correctly

### 5. Reports and Management

**Reorder Reports:**
- Display `min_qty` from `location_stocks` table
- "Edit Min" button updates `location_stocks.min_qty` instead of `products.min_qty`
- CSV exports include the correct min_qty values

**Updated Functions:**
- `updateMinQuantity()` - Now updates `location_stocks.min_qty` for all locations of a product

## Benefits of the New System

### 1. Simplified Logic
- **Single Source of Truth:** Only need to check `location_stocks` table for reorder levels
- **Location-Specific:** Each location can have different minimum quantities if needed
- **Consistent Data:** Stock and min_qty are in the same table

### 2. Automatic Setup
- **No Manual Stock Entry:** Initial stock is automatically set to max_qty
- **Consistent Min Quantities:** Min_qty is automatically copied to all location stocks
- **Reduced Errors:** Less manual data entry reduces setup mistakes

### 3. Better Performance
- **Fewer Joins:** Reorder checking requires fewer table joins
- **Faster Queries:** All required data is in `location_stocks` table
- **Simplified Conditions:** Cleaner WHERE clauses

### 4. Enhanced Flexibility
- **Location-Specific Min Quantities:** Future enhancement possibility
- **Independent Stock Management:** Each location's stock is self-contained
- **Easier Reporting:** All stock data in one place

## Implementation Details

### Files Modified

1. **Database Schema:**
   - `src/database.js` - Added min_qty column and migration logic

2. **Product Management:**
   - `src/database.js` - Updated createLocationStocks and updateLocationStocks
   - `src/pages/admin/master/setup-product.js` - Auto-assign max_qty as initial stock
   - `src/index.js` - Updated IPC handlers to pass min_qty and max_qty

3. **Inventory Service:**
   - `src/services/inventoryService.js` - Updated reorder checking logic
   - `src/services/inventoryService.js` - Updated updateMinQuantity to use location_stocks

4. **Reports:**
   - Reports automatically work with new system (no changes needed)
   - Min quantity updates now affect location_stocks table

### Testing

Run the test script to validate the complete flow:
```bash
node test-new-reorder-system.js
```

The test covers:
- Product creation with min_qty and max_qty
- Automatic stock assignment
- Sales processing and stock reduction
- Reorder level detection
- Min quantity updates

## Migration Notes

- **Backward Compatibility:** Existing products will have their min_qty migrated automatically
- **Zero Downtime:** Migration runs automatically on database initialization
- **Data Integrity:** All existing reorder alerts will continue to work
- **No User Action Required:** System automatically handles the transition

## Future Enhancements

1. **Location-Specific Min Quantities:** Allow different min_qty per location
2. **Bulk Min Quantity Updates:** Update min_qty for multiple products at once
3. **Historical Reorder Tracking:** Track reorder level changes over time
4. **Advanced Reorder Algorithms:** Consider sales velocity and seasonality
