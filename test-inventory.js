/**
 * Test script to verify inventory system
 * Run this to check if inventory tracking is working correctly
 */

const Database = require('./src/database');
const InventoryService = require('./src/services/inventoryService');

async function testInventorySystem() {
    console.log('🧪 Testing Inventory System...\n');
    
    try {
        // Initialize database
        const db = new Database();
        db.init();
        
        // Initialize inventory service
        const inventoryService = new InventoryService(db);
        
        console.log('✅ Services initialized\n');
        
        // Test 1: Check current products and their stock
        console.log('📊 TEST 1: Current Products and Stock Levels');
        console.log('=' .repeat(50));
        
        const products = await db.getAllProducts();
        console.log(`Found ${products.length} products in database`);
        
        for (const product of products.slice(0, 5)) { // Show first 5 products
            console.log(`\nProduct: ${product.description}`);
            console.log(`  ID: ${product.id}`);
            console.log(`  Barcode: ${product.barcode}`);
            console.log(`  Min Qty: ${product.min_qty}`);
            console.log(`  Max Qty: ${product.max_qty}`);
            
            // Get location stocks for this product
            const locationStocks = await db.getLocationStocksByProductId(product.id);
            if (locationStocks.length > 0) {
                locationStocks.forEach(stock => {
                    console.log(`  Stock at ${stock.location}: ${stock.stock}`);
                    console.log(`  Price at ${stock.location}: $${stock.price}`);
                });
            } else {
                console.log('  No location stock records found');
            }
        }
        
        console.log('\n📊 TEST 2: Check Reorder Levels');
        console.log('=' .repeat(50));
        
        const reorderReport = await inventoryService.getReorderReport();
        console.log(`\nReorder Report Generated:`);
        console.log(`  Total products checked: ${reorderReport.total_products_checked}`);
        console.log(`  Critical stock items: ${reorderReport.critical_stock.length}`);
        console.log(`  Low stock items: ${reorderReport.low_stock.length}`);
        console.log(`  Total reorder value: $${reorderReport.total_reorder_value.toFixed(2)}`);
        
        if (reorderReport.alerts.length > 0) {
            console.log('\n🚨 REORDER ALERTS:');
            reorderReport.alerts.forEach(alert => {
                console.log(`  - ${alert.description} (${alert.barcode})`);
                console.log(`    Location: ${alert.location}`);
                console.log(`    Current Stock: ${alert.stock}`);
                console.log(`    Min Qty: ${alert.min_qty}`);
                console.log(`    Status: ${alert.stock_status.toUpperCase()}`);
                console.log(`    Reorder Qty: ${alert.reorder_quantity}`);
                console.log('');
            });
        } else {
            console.log('\n✅ No reorder alerts - all stock levels are good!');
        }
        
        console.log('\n📊 TEST 3: Simulate Sale and Check Inventory Update');
        console.log('=' .repeat(50));
        
        // Find a product with stock to test
        const testProduct = products.find(p => p.min_qty > 0);
        if (testProduct) {
            console.log(`\nTesting with product: ${testProduct.description}`);
            
            // Get current stock
            const locationStocks = await db.getLocationStocksByProductId(testProduct.id);
            if (locationStocks.length > 0) {
                const testLocation = locationStocks[0].location;
                const currentStock = locationStocks[0].stock;
                
                console.log(`Current stock at ${testLocation}: ${currentStock}`);
                console.log(`Min quantity: ${testProduct.min_qty}`);
                
                // Simulate a sale
                const saleItems = [{
                    productId: testProduct.id,
                    name: testProduct.description,
                    quantity: 1
                }];
                
                console.log('\nSimulating sale of 1 unit...');
                const updateResult = await inventoryService.updateInventoryAfterSale(saleItems, testLocation);
                
                if (updateResult.success) {
                    console.log('✅ Inventory update successful');
                    
                    // Check new reorder levels
                    const newReorderReport = await inventoryService.getReorderReport();
                    console.log(`\nAfter sale - Reorder alerts: ${newReorderReport.alerts.length}`);
                    
                    if (newReorderReport.alerts.length > 0) {
                        console.log('🚨 New reorder alerts generated!');
                        newReorderReport.alerts.forEach(alert => {
                            if (alert.id === testProduct.id) {
                                console.log(`  - ${alert.description}: Stock ${alert.stock}, Min ${alert.min_qty}`);
                            }
                        });
                    }
                } else {
                    console.log('❌ Inventory update failed:', updateResult.error);
                }
            } else {
                console.log('⚠️ No location stock found for test product');
            }
        } else {
            console.log('⚠️ No suitable test product found (need product with min_qty > 0)');
        }
        
        console.log('\n🧪 Test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testInventorySystem().then(() => {
    console.log('\n✅ Test script finished');
    process.exit(0);
}).catch(error => {
    console.error('❌ Test script error:', error);
    process.exit(1);
});
