<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rainbow Station Inc - Welcome</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #000000 0%, #001100 25%, #002200 50%, #001100 75%, #000000 100%);
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Animated background particles */
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            background: rgba(0, 255, 0, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) { width: 20px; height: 20px; top: 20%; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 15px; height: 15px; top: 60%; left: 80%; animation-delay: 2s; }
        .particle:nth-child(3) { width: 25px; height: 25px; top: 80%; left: 20%; animation-delay: 4s; }
        .particle:nth-child(4) { width: 18px; height: 18px; top: 30%; left: 70%; animation-delay: 1s; }
        .particle:nth-child(5) { width: 22px; height: 22px; top: 70%; left: 50%; animation-delay: 3s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.3; }
        }

        /* Window Controls */
        .window-controls {
            position: fixed;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .minimize-btn {
            background: linear-gradient(135deg, #ffaa00, #ff8800);
            color: white;
        }

        .close-btn {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            color: white;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }

        /* Main Container */
        .main-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            position: relative;
            z-index: 1;
            padding: 40px;
        }

        /* Logo/Image Section */
        .logo-section {
            margin-bottom: 50px;
            animation: logoGlow 3s ease-in-out infinite alternate;
        }

        .logo-image {
            font-size: 120px;
            margin-bottom: 30px;
            filter: drop-shadow(0 0 30px rgba(0, 255, 0, 0.6));
        }

        @keyframes logoGlow {
            from {
                filter: drop-shadow(0 0 30px rgba(0, 255, 0, 0.6));
            }
            to {
                filter: drop-shadow(0 0 50px rgba(0, 255, 0, 0.9)) drop-shadow(0 0 70px rgba(0, 255, 0, 0.3));
            }
        }

        /* Title Section */
        .title-section {
            margin-bottom: 60px;
        }

        .main-title {
            font-size: 48px;
            color: #00ff00;
            text-shadow: 0 0 30px rgba(0, 255, 0, 0.8);
            font-weight: 300;
            margin-bottom: 20px;
            animation: titlePulse 4s ease-in-out infinite;
        }

        .subtitle {
            font-size: 24px;
            color: rgba(0, 255, 0, 0.7);
            font-weight: 200;
            margin-bottom: 10px;
        }

        .description {
            font-size: 18px;
            color: rgba(0, 255, 0, 0.5);
            font-weight: 200;
            max-width: 600px;
            line-height: 1.6;
        }

        @keyframes titlePulse {
            0%, 100% { 
                text-shadow: 0 0 30px rgba(0, 255, 0, 0.8);
                transform: scale(1);
            }
            50% { 
                text-shadow: 0 0 50px rgba(0, 255, 0, 1), 0 0 70px rgba(0, 255, 0, 0.5);
                transform: scale(1.02);
            }
        }

        /* Login Button */
        .login-button {
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.2), rgba(0, 255, 0, 0.4));
            border: 3px solid rgba(0, 255, 0, 0.6);
            border-radius: 50px;
            color: #00ff00;
            font-size: 24px;
            font-weight: 600;
            padding: 20px 60px;
            cursor: pointer;
            transition: all 0.4s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 0 30px rgba(0, 255, 0, 0.3),
                inset 0 0 20px rgba(0, 255, 0, 0.1);
            animation: buttonGlow 2s ease-in-out infinite alternate;
        }

        .login-button:hover {
            background: linear-gradient(135deg, rgba(0, 255, 0, 0.4), rgba(0, 255, 0, 0.6));
            border-color: #00ff00;
            transform: translateY(-5px) scale(1.05);
            box-shadow: 
                0 10px 40px rgba(0, 255, 0, 0.5),
                inset 0 0 30px rgba(0, 255, 0, 0.2);
            text-shadow: 0 0 20px rgba(0, 255, 0, 0.8);
        }

        .login-button:active {
            transform: translateY(-2px) scale(1.02);
        }

        @keyframes buttonGlow {
            from {
                box-shadow: 
                    0 0 30px rgba(0, 255, 0, 0.3),
                    inset 0 0 20px rgba(0, 255, 0, 0.1);
            }
            to {
                box-shadow: 
                    0 0 50px rgba(0, 255, 0, 0.5),
                    inset 0 0 30px rgba(0, 255, 0, 0.2);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-title {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 20px;
            }
            
            .description {
                font-size: 16px;
            }
            
            .login-button {
                font-size: 20px;
                padding: 15px 40px;
            }
            
            .logo-image {
                font-size: 80px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 28px;
            }
            
            .subtitle {
                font-size: 18px;
            }
            
            .description {
                font-size: 14px;
            }
            
            .login-button {
                font-size: 18px;
                padding: 12px 30px;
            }
            
            .logo-image {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <!-- Background Animation -->
    <div class="background-animation">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Window Controls -->
    <div class="window-controls">
        <button class="control-btn minimize-btn" onclick="minimizeApp()" title="Minimize">−</button>
        <button class="control-btn close-btn" onclick="closeApp()" title="Close">×</button>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-image">🏪</div>
        </div>

        <!-- Title Section -->
        <div class="title-section">
            <h1 class="main-title">Rainbow Station Inc</h1>
            <h2 class="subtitle">Point of Sale System</h2>
            <p class="description">
                Welcome to our modern, secure, and reliable point of sale system. 
                Experience seamless transactions with advanced features designed for your business success.
            </p>
        </div>

        <!-- Login Button -->
        <button class="login-button" onclick="goToLogin()">
            Enter System
        </button>
    </div>

    <script src="welcome.js"></script>
</body>
</html>
