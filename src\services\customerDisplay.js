/**
 * Customer Display Service
 * Supports VFD and LCD customer displays via serial/USB communication
 */

class CustomerDisplayService {
    constructor() {
        this.isConnected = false;
        this.displayPort = null;
        this.displayType = 'vfd'; // 'vfd' or 'lcd'
        this.displayWidth = 20; // characters per line
        this.displayHeight = 2; // number of lines
        this.currentContent = ['', ''];
        
        // Display configuration
        this.config = {
            port: 'COM3',
            baudRate: 9600,
            dataBits: 8,
            parity: 'none',
            stopBits: 1,
            autoConnect: true,
            scrollSpeed: 500, // ms for scrolling text
            brightness: 100 // 0-100%
        };
        
        console.log('🖥️ Customer Display Service initialized');
    }

    /**
     * Initialize and connect to customer display
     */
    async initialize() {
        try {
            console.log('🖥️ Initializing customer display...');
            
            if (this.config.autoConnect) {
                await this.connect();
            }
            
            return {
                success: true,
                connected: this.isConnected,
                displayType: this.displayType
            };
            
        } catch (error) {
            console.error('❌ Failed to initialize customer display:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Connect to customer display
     */
    async connect(portName = null) {
        try {
            const port = portName || this.config.port;
            console.log(`🔌 Connecting to customer display on ${port}...`);
            
            // For now, simulate connection since we don't have serialport
            // In real implementation, use: const SerialPort = require('serialport');
            
            this.isConnected = true;
            this.displayPort = { port }; // Simulated port object
            
            // Initialize display
            await this.initializeDisplay();
            
            // Show welcome message
            await this.showWelcome();
            
            console.log('✅ Customer display connected successfully');
            return true;
            
        } catch (error) {
            console.error('❌ Failed to connect to customer display:', error);
            this.isConnected = false;
            return false;
        }
    }

    /**
     * Initialize display with proper settings
     */
    async initializeDisplay() {
        if (!this.isConnected) return;
        
        try {
            // Clear display
            await this.clearDisplay();
            
            // Set brightness (if supported)
            await this.setBrightness(this.config.brightness);
            
            // Set cursor off (if supported)
            await this.sendCommand('\x0C'); // Form feed - clear screen
            
        } catch (error) {
            console.error('❌ Error initializing display:', error);
        }
    }

    /**
     * Send command to display
     */
    async sendCommand(command) {
        if (!this.isConnected || !this.displayPort) {
            console.warn('⚠️ Display not connected');
            return false;
        }
        
        try {
            // In real implementation:
            // this.displayPort.write(command);
            console.log(`📤 Display command: ${command.replace(/\r/g, '\\r').replace(/\n/g, '\\n')}`);
            return true;
            
        } catch (error) {
            console.error('❌ Error sending command to display:', error);
            return false;
        }
    }

    /**
     * Display text on specific line
     */
    async displayText(line1, line2 = '') {
        if (!this.isConnected) {
            console.warn('⚠️ Display not connected, cannot show text');
            return false;
        }
        
        try {
            // Truncate text to display width
            const text1 = this.formatLine(line1);
            const text2 = this.formatLine(line2);
            
            // Store current content
            this.currentContent = [text1, text2];
            
            // Send to display
            const command = `\x0C${text1}\r\n${text2}`;
            await this.sendCommand(command);
            
            console.log(`🖥️ Display updated: "${text1}" | "${text2}"`);
            return true;
            
        } catch (error) {
            console.error('❌ Error displaying text:', error);
            return false;
        }
    }

    /**
     * Show transaction details
     */
    async showTransaction(transactionData) {
        const { items, total, subtotal, tax } = transactionData;
        
        if (items && items.length > 0) {
            const lastItem = items[items.length - 1];
            const line1 = `${lastItem.name}`.substring(0, this.displayWidth);
            const line2 = `$${parseFloat(lastItem.price || 0).toFixed(2)} Total: $${parseFloat(total || 0).toFixed(2)}`;
            
            await this.displayText(line1, line2);
        } else {
            await this.displayText('Welcome!', `Total: $${parseFloat(total || 0).toFixed(2)}`);
        }
    }

    /**
     * Show payment information
     */
    async showPayment(paymentData) {
        const { method, amount, change, total } = paymentData;
        
        const line1 = `${method}: $${parseFloat(amount || 0).toFixed(2)}`;
        const line2 = change > 0 ? `Change: $${parseFloat(change).toFixed(2)}` : 'Thank You!';
        
        await this.displayText(line1, line2);
    }

    /**
     * Show welcome message
     */
    async showWelcome() {
        await this.displayText('Rainbow Station Inc', 'Welcome!');
    }

    /**
     * Show checkout completion
     */
    async showCheckoutComplete(saleData) {
        const total = parseFloat(saleData.total_amount || 0).toFixed(2);
        await this.displayText('Sale Complete!', `Total: $${total}`);
        
        // Return to welcome after 5 seconds
        setTimeout(() => {
            this.showWelcome();
        }, 5000);
    }

    /**
     * Clear display
     */
    async clearDisplay() {
        await this.sendCommand('\x0C'); // Form feed
        this.currentContent = ['', ''];
    }

    /**
     * Set display brightness
     */
    async setBrightness(level) {
        // VFD displays often support brightness control
        if (this.displayType === 'vfd') {
            const brightness = Math.max(0, Math.min(100, level));
            const command = `\x1B\x2A${String.fromCharCode(brightness)}`;
            await this.sendCommand(command);
        }
    }

    /**
     * Format line to fit display width
     */
    formatLine(text) {
        if (!text) return '';
        
        // Pad or truncate to display width
        const formatted = text.toString().substring(0, this.displayWidth);
        return formatted.padEnd(this.displayWidth, ' ');
    }

    /**
     * Scroll long text
     */
    async scrollText(text, line = 1) {
        if (text.length <= this.displayWidth) {
            return;
        }
        
        const scrollText = text + '    '; // Add spacing
        for (let i = 0; i <= scrollText.length - this.displayWidth; i++) {
            const segment = scrollText.substring(i, i + this.displayWidth);
            
            if (line === 1) {
                await this.displayText(segment, this.currentContent[1]);
            } else {
                await this.displayText(this.currentContent[0], segment);
            }
            
            await new Promise(resolve => setTimeout(resolve, this.config.scrollSpeed));
        }
    }

    /**
     * Test display functionality
     */
    async testDisplay() {
        if (!this.isConnected) {
            await this.connect();
        }
        
        await this.displayText('Testing Display...', 'Line 2 Test');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await this.displayText('Character Test:', '0123456789ABCDEF');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await this.showWelcome();
        
        return true;
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('🔧 Customer display configuration updated');
    }

    /**
     * Get display status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            port: this.config.port,
            displayType: this.displayType,
            dimensions: `${this.displayWidth}x${this.displayHeight}`,
            currentContent: this.currentContent
        };
    }

    /**
     * Disconnect from display
     */
    async disconnect() {
        if (this.isConnected && this.displayPort) {
            try {
                await this.clearDisplay();
                // In real implementation: this.displayPort.close();
                this.isConnected = false;
                this.displayPort = null;
                console.log('🔌 Customer display disconnected');
            } catch (error) {
                console.error('❌ Error disconnecting display:', error);
            }
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomerDisplayService;
} else {
    window.CustomerDisplayService = CustomerDisplayService;
}
