# 📧 Email Service Setup Guide - Rainbow Station Inc POS

## Overview
This guide explains how to set up the Resend email service integration for automatically sending daily sales reports and shift performance reports to the business owner.

## 🚀 Quick Setup

### 1. Get Resend API Key
1. Visit [Resend.com](https://resend.com)
2. Create an account or sign in
3. Go to [API Keys](https://resend.com/api-keys)
4. Click "Create API Key"
5. Give it a name like "Rainbow Station POS"
6. Copy the API key (starts with `re_`)

### 2. Configure Domain (Optional but Recommended)
1. Go to [Domains](https://resend.com/domains) in Resend
2. Add your domain (e.g., `rainbowstationinc.com`)
3. Follow DNS verification steps
4. This allows you to send from `<EMAIL>`

### 3. Configure Environment Variables
Create or edit the `.env.local` file in your application root:

```env
# Resend Email Service Configuration
RESEND_API_KEY=re_your_actual_api_key_here

# Business Owner Email Configuration
BUSINESS_OWNER_EMAIL=<EMAIL>
BUSINESS_OWNER_NAME=Rainbow Station Inc Owner

# Email Sender Configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Rainbow Station POS System

# Email Settings
EMAIL_ENABLED=true
```

### 4. Restart Application
After configuring the `.env.local` file, restart the POS application for changes to take effect.

## 📊 Features

### Daily Sales Report Email
- **Trigger**: Click "📧 Email Report" button in Admin Panel → Reports → Daily Sales Report
- **Content**: 
  - HTML email with sales summary
  - CSV attachment with detailed data
  - Breakdown by sale types (Regular, Theater, Deli)
  - Payment method totals (Cash, Debit, Credit)

### Shift Sales Report Email
- **Trigger**: Click "📧 Email Report" button in Admin Panel → Reports → Shift Performance Report
- **Content**:
  - HTML email with shift summary
  - CSV attachment with transaction details
  - Operator and location information
  - Transaction breakdown by type

### Quick Actions
- **Test Email Service**: Sends a test email to verify configuration
- **Email Config**: Shows current configuration status

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `RESEND_API_KEY` | Your Resend API key | `re_123abc...` |
| `BUSINESS_OWNER_EMAIL` | Email to receive reports | `<EMAIL>` |
| `BUSINESS_OWNER_NAME` | Name for email recipient | `John Doe` |
| `EMAIL_FROM_ADDRESS` | Sender email address | `<EMAIL>` |
| `EMAIL_FROM_NAME` | Sender display name | `POS System` |
| `EMAIL_ENABLED` | Enable/disable email service | `true` or `false` |

### Email Templates
The system includes professional HTML email templates with:
- Company branding (Rainbow Station Inc)
- Sales summaries with color-coded sections
- Responsive design for mobile devices
- Automatic timestamp and report details

## 🛠️ Troubleshooting

### Common Issues

#### 1. "Email service not initialized"
- **Cause**: Missing or invalid API key
- **Solution**: Check `.env.local` file and restart application

#### 2. "Email service not enabled"
- **Cause**: `EMAIL_ENABLED` is set to `false`
- **Solution**: Set `EMAIL_ENABLED=true` in `.env.local`

#### 3. "Failed to send email: Invalid API key"
- **Cause**: Incorrect Resend API key
- **Solution**: Verify API key in Resend dashboard

#### 4. "Domain not verified"
- **Cause**: Using custom domain without verification
- **Solution**: Use `<EMAIL>` or verify your domain

### Testing Email Service
1. Go to Admin Panel → Quick Actions
2. Click "📧 Test Email Service"
3. Check if test email is received
4. Verify configuration if test fails

### Checking Email Status
1. Go to Admin Panel → Quick Actions
2. Click "⚙️ Email Config"
3. Review configuration details
4. Follow setup instructions if needed

## 📋 Email Content Examples

### Daily Sales Report Email
```
Subject: Daily Sales Report - 2024-01-15 to 2024-01-15

🌈 Rainbow Station Inc
Daily Sales Report
2024-01-15 to 2024-01-15

📊 Sales Summary
Total Sales: $1,234.56
Cash: $456.78
Debit: $389.12
Credit: $388.66

🏪 Sales by Type
Regular Sales: $678.90 (12 transactions)
Theater: $336.00 (6 tickets)
Deli: $219.66 (8 transactions)

📎 Attachment: Detailed CSV report is attached to this email.
```

### Shift Sales Report Email
```
Subject: Shift Sales Report - SHIFT-1705123456789

🌈 Rainbow Station Inc
Shift Sales Report
Shift ID: SHIFT-1705123456789

👤 Shift Information
Operator: John Doe
Location: Main Store
Start Time: 1/13/2024, 9:00:00 AM
End Time: 1/13/2024, 5:00:00 PM
Status: completed

📊 Sales Summary
Total Sales: $856.34
Total Transactions: 15

📎 Attachment: Detailed CSV report is attached to this email.
```

## 🔒 Security Notes

- API keys are stored locally in `.env.local`
- Emails are sent directly to Resend servers
- No sensitive data is logged in email content
- CSV attachments contain business data - ensure recipient email security

## 📞 Support

For technical support with email integration:
1. Check this documentation first
2. Verify Resend service status at [status.resend.com](https://status.resend.com)
3. Contact your system administrator
4. Review application logs for detailed error messages

## 🔄 Updates

The email service will automatically:
- Include latest sales data in reports
- Use current user location and operator information
- Apply any active filters from report generation
- Format data according to business requirements

---

**Note**: This email service requires an active internet connection and valid Resend account. Ensure your firewall allows outbound HTTPS connections to `api.resend.com`.
