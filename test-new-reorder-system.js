/**
 * Test script for the new reorder level checking method
 * 
 * This script tests the complete flow:
 * 1. Product creation with min_qty and max_qty
 * 2. Automatic assignment of max_qty to location_stocks.stock
 * 3. Automatic assignment of min_qty to location_stocks.min_qty
 * 4. Sales processing and stock reduction
 * 5. Reorder level detection based on location_stocks.min_qty
 */

const Database = require('./src/database');
const InventoryService = require('./src/services/inventoryService');
const path = require('path');

async function testNewReorderSystem() {
    console.log('🧪 Testing New Reorder Level System');
    console.log('=====================================\n');

    // Initialize database
    const dbPath = path.join(__dirname, 'test_reorder_system.db');
    const db = new Database(dbPath);
    
    // Wait for database initialization
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const inventoryService = new InventoryService(db);
    
    try {
        console.log('📝 Step 1: Creating a test product with min_qty=5 and max_qty=20');
        
        const productData = {
            barcode: 'TEST-REORDER-001',
            description: 'Test Product for Reorder System',
            category: 'Test Category',
            subcategory: 'Test Subcategory',
            supplier: 'Test Supplier',
            purchase_price: 10.00,
            style: 'Test Style',
            color: 'Red',
            size: 'Medium',
            min_qty: 5,  // This will be copied to location_stocks.min_qty
            max_qty: 20, // This will be used as initial stock
            image_path: null,
            special_discount: false,
            priority: false,
            image_confirm: false,
            non_scanable: false,
            daily_item: false
        };

        const product = await db.createProduct(productData);
        console.log(`✅ Product created with ID: ${product.id}`);

        console.log('\n📦 Step 2: Creating location stocks with automatic assignment');
        
        const locationStocks = [
            { location: 'Test Location 1', stock: 0, price: 15.00 },
            { location: 'Test Location 2', stock: 0, price: 16.00 }
        ];

        // This should automatically set stock to max_qty (20) and min_qty to 5
        await db.createLocationStocks(product.id, locationStocks, productData.min_qty, productData.max_qty);
        console.log('✅ Location stocks created with automatic assignment');

        console.log('\n🔍 Step 3: Verifying location stocks data');
        const stocks = await db.getLocationStocksByProductId(product.id);
        stocks.forEach(stock => {
            console.log(`   Location: ${stock.location}`);
            console.log(`   Stock: ${stock.stock} (should be ${productData.max_qty})`);
            console.log(`   Min Qty: ${stock.min_qty} (should be ${productData.min_qty})`);
            console.log(`   Price: $${stock.price}`);
            console.log('   ---');
        });

        console.log('\n📊 Step 4: Checking initial reorder levels');
        const initialReorderReport = await inventoryService.getReorderReport('Test Location 1');
        console.log(`Initial reorder alerts: ${initialReorderReport.alerts.length} (should be 0)`);

        console.log('\n🛒 Step 5: Simulating sales to reduce stock below reorder level');
        
        // Simulate selling 16 units (20 - 16 = 4, which is below min_qty of 5)
        const saleItems = [
            { productId: product.id, quantity: 16, name: 'Test Product for Reorder System' }
        ];

        console.log('   Selling 16 units (20 - 16 = 4, below min_qty of 5)...');
        const inventoryResult = await inventoryService.updateInventoryAfterSale(saleItems, 'Test Location 1');
        
        if (inventoryResult.success) {
            console.log('✅ Inventory updated successfully after sale');
        } else {
            console.log('❌ Inventory update failed:', inventoryResult.error);
        }

        console.log('\n🚨 Step 6: Checking reorder levels after sale');
        const finalReorderReport = await inventoryService.getReorderReport('Test Location 1');
        console.log(`Final reorder alerts: ${finalReorderReport.alerts.length} (should be 1)`);
        
        if (finalReorderReport.alerts.length > 0) {
            const alert = finalReorderReport.alerts[0];
            console.log('\n📋 Reorder Alert Details:');
            console.log(`   Product: ${alert.description}`);
            console.log(`   Location: ${alert.location}`);
            console.log(`   Current Stock: ${alert.stock}`);
            console.log(`   Min Qty (from location_stocks): ${alert.min_qty}`);
            console.log(`   Max Qty: ${alert.max_qty}`);
            console.log(`   Status: ${alert.stock_status}`);
            console.log(`   Reorder Quantity: ${alert.reorder_quantity}`);
        }

        console.log('\n🧪 Step 7: Testing min_qty update in location_stocks');
        console.log('   Updating min_qty from 5 to 3...');
        await inventoryService.updateMinQuantity(product.id, 3);
        
        const updatedReorderReport = await inventoryService.getReorderReport('Test Location 1');
        console.log(`Reorder alerts after min_qty update: ${updatedReorderReport.alerts.length} (should be 0 since stock=4 > new min_qty=3)`);

        console.log('\n✅ Test completed successfully!');
        console.log('\n📋 Summary of New Reorder System:');
        console.log('   1. ✅ Product min_qty and max_qty are stored in products table');
        console.log('   2. ✅ max_qty is automatically assigned as initial stock in location_stocks');
        console.log('   3. ✅ min_qty is automatically assigned to location_stocks.min_qty');
        console.log('   4. ✅ Sales reduce stock in location_stocks table');
        console.log('   5. ✅ Reorder level checking uses location_stocks.min_qty (not products.min_qty)');
        console.log('   6. ✅ Min quantity updates affect location_stocks.min_qty');

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Clean up
        if (db && db.db) {
            db.db.close();
        }
    }
}

// Run the test
if (require.main === module) {
    testNewReorderSystem();
}

module.exports = testNewReorderSystem;
