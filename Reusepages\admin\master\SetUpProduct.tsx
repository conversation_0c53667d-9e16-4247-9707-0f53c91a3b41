import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, ChevronDown } from "lucide-react"

interface LocationStock {
  location: string
  stock: number
  price: number
}

export default function SetUpProduct() {
  const [formData, setFormData] = useState({
    barcode: "",
    description: "",
    category: "",
    subCategory: "",
    supplier: "",
    purchasePrice: "",
    style: "",
    color: "",
    size: "",
    minQty: "",
    maxQty: "",
    specialDiscount: false,
    priority: false,
    imageConfirm: false,
    nonScanable: false,
    deliItem: false
  })

  const [locationStocks] = useState<LocationStock[]>([
    { location: "Taboo-Atlantic City-NJ", stock: 0, price: 0.00 },
    { location: "Huntington", stock: 0, price: 0.00 },
    { location: "Miami Playground-Florida", stock: 0, price: 0.00 },
    { location: "LOVE TOYS", stock: 0, price: 0.00 },
    { location: "Pompano Beach-Florida", stock: 0, price: 0.00 },
    { location: "Rainbow Station-New York", stock: 0, price: 0.00 }
  ])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    console.log("Saving product:", formData)
  }

  const handleClear = () => {
    setFormData({
      barcode: "",
      description: "",
      category: "",
      subCategory: "",
      supplier: "",
      purchasePrice: "",
      style: "",
      color: "",
      size: "",
      minQty: "",
      maxQty: "",
      specialDiscount: false,
      priority: false,
      imageConfirm: false,
      nonScanable: false,
      deliItem: false
    })
  }

  const handleDelete = () => {
    console.log("Deleting product")
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Set Up Product</h2>

      {/* Top Action Buttons */}
      <div className="flex gap-3 mb-6">
        <Button
          onClick={handleSave}
          className="bg-green-600 hover:bg-green-700 text-white font-bold px-8 py-2"
        >
          SAVE
        </Button>
        <Button
          onClick={handleClear}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold px-8 py-2"
        >
          CLEAR
        </Button>
        <Button
          onClick={handleDelete}
          className="bg-red-600 hover:bg-red-700 text-white font-bold px-8 py-2"
        >
          DELETE
        </Button>
      </div>

      {/* Main Form */}
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg">
        <div className="p-6 space-y-6">
          {/* Barcode Row */}
          <div className="grid grid-cols-12 gap-4 items-end">
            <div className="col-span-3">
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Barcode</Label>
              <div className="flex gap-2">
                <Input
                  value={formData.barcode}
                  onChange={(e) => handleInputChange("barcode", e.target.value)}
                  className="flex-1 border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter barcode"
                />
                <Button className="bg-gray-600 hover:bg-gray-700 text-white px-3">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Description Row */}
          <div>
            <Label className="text-sm font-bold text-gray-700 mb-2 block">Description</Label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              className="w-full border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
              rows={4}
              placeholder="Enter product description"
            />
          </div>

          {/* Category and Sub Category Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Category</Label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange("category", e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Select Category</option>
                <option value="A-pipes">A. Pipes</option>
                <option value="A-deli">A. Deli</option>
                <option value="A-pills">A. Pills</option>
                <option value="B-nailpolish">B. Nailpolish Remo</option>
                <option value="black-unicorn">Black Unicorn</option>
                <option value="C-misc">C. Miscellaneous</option>
              </select>
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Sub Category</Label>
              <select
                value={formData.subCategory}
                onChange={(e) => handleInputChange("subCategory", e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Select Sub Category</option>
                <option value="water-pipes">Water Pipes</option>
                <option value="dry-pipes">Dry Pipes</option>
                <option value="metal-pipes">Metal Pipes</option>
                <option value="glass-pipes">Glass Pipes</option>
              </select>
            </div>
          </div>

          {/* Supplier and Purchase Price Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Supplier</Label>
              <div className="flex gap-2">
                <select
                  value={formData.supplier}
                  onChange={(e) => handleInputChange("supplier", e.target.value)}
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="">Select Supplier</option>
                  <option value="SUP001">ABC Wholesale</option>
                  <option value="SUP002">XYZ Distributors</option>
                  <option value="SUP003">Premium Supplies Co</option>
                </select>
                <Button className="bg-gray-600 hover:bg-gray-700 text-white px-3">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Purchase Price</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.purchasePrice}
                onChange={(e) => handleInputChange("purchasePrice", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Style and Color Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Style</Label>
              <Input
                value={formData.style}
                onChange={(e) => handleInputChange("style", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter style"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Color</Label>
              <div className="flex gap-2">
                <Input
                  value={formData.color}
                  onChange={(e) => handleInputChange("color", e.target.value)}
                  className="flex-1 border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter color"
                />
                <Button className="bg-gray-600 hover:bg-gray-700 text-white px-3">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Size, Min Qty, Max Qty Row */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Size</Label>
              <Input
                value={formData.size}
                onChange={(e) => handleInputChange("size", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter size"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Min Qty</Label>
              <Input
                type="number"
                value={formData.minQty}
                onChange={(e) => handleInputChange("minQty", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="0"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Max Qty</Label>
              <Input
                type="number"
                value={formData.maxQty}
                onChange={(e) => handleInputChange("maxQty", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="0"
              />
            </div>
          </div>

          {/* Image and Checkboxes Section */}
          <div className="grid grid-cols-2 gap-6">
            {/* Image Section */}
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Image</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50">
                <div className="text-gray-500 mb-4">
                  <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600 mb-4">No image selected</p>
                <div className="flex gap-2 justify-center">
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold px-4 py-2">
                    BROWSE
                  </Button>
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white font-bold px-4 py-2">
                    CLEAR
                  </Button>
                </div>
              </div>
            </div>

            {/* Checkboxes Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="specialDiscount"
                  checked={formData.specialDiscount}
                  onCheckedChange={(checked) => handleInputChange("specialDiscount", checked)}
                  className="w-5 h-5"
                />
                <Label htmlFor="specialDiscount" className="text-sm font-bold text-gray-700">
                  Special Discount
                </Label>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="priority"
                  checked={formData.priority}
                  onCheckedChange={(checked) => handleInputChange("priority", checked)}
                  className="w-5 h-5"
                />
                <Label htmlFor="priority" className="text-sm font-bold text-gray-700">
                  Priority
                </Label>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="imageConfirm"
                  checked={formData.imageConfirm}
                  onCheckedChange={(checked) => handleInputChange("imageConfirm", checked)}
                  className="w-5 h-5"
                />
                <Label htmlFor="imageConfirm" className="text-sm font-bold text-gray-700">
                  Image Confirm
                </Label>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="nonScanable"
                  checked={formData.nonScanable}
                  onCheckedChange={(checked) => handleInputChange("nonScanable", checked)}
                  className="w-5 h-5"
                />
                <Label htmlFor="nonScanable" className="text-sm font-bold text-gray-700">
                  Non Scanable
                </Label>
              </div>

              <div className="flex items-center space-x-3">
                <Checkbox
                  id="deliItem"
                  checked={formData.deliItem}
                  onCheckedChange={(checked) => handleInputChange("deliItem", checked)}
                  className="w-5 h-5"
                />
                <Label htmlFor="deliItem" className="text-sm font-bold text-gray-700">
                  Deli Item
                </Label>
              </div>
            </div>
          </div>

          {/* Selling Price Section */}
          <div>
            <Label className="text-lg font-bold text-gray-800 mb-4 block">Selling Price</Label>
            <div className="border border-gray-300 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-100 border-b border-gray-300">
                    <th className="text-left p-3 font-bold text-gray-700 border-r border-gray-300">Location</th>
                    <th className="text-center p-3 font-bold text-gray-700 border-r border-gray-300">Stock</th>
                    <th className="text-center p-3 font-bold text-gray-700">Price</th>
                  </tr>
                </thead>
                <tbody>
                  {locationStocks.map((location, index) => (
                    <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="p-3 font-medium text-gray-800 border-r border-gray-300">
                        {location.location}
                      </td>
                      <td className="p-2 text-center border-r border-gray-300">
                        <Input
                          type="number"
                          value={location.stock}
                          className="w-20 text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="0"
                        />
                      </td>
                      <td className="p-2 text-center">
                        <Input
                          type="number"
                          step="0.01"
                          value={location.price.toFixed(2)}
                          className="w-24 text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="0.00"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
