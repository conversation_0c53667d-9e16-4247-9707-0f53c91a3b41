# 🔧 Login & Database Troubleshooting Guide

## 🚨 **CRITICAL FIXES APPLIED**

The following issues have been identified and fixed in the latest build:

### ❌ **Previous Issues:**
1. **Database Path Problem**: Database was trying to save in read-only ASAR archive
2. **File Permissions**: No write access to application directory
3. **Missing Database**: Database file not created in accessible location

### ✅ **Fixes Applied:**
1. **Database Location**: Now saves to user data directory (`%APPDATA%/rainbow-station-inc-pos/`)
2. **Write Permissions**: Full read/write access to user data folder
3. **Enhanced Logging**: Better error messages and debugging info

---

## 🔑 **DEFAULT LOGIN CREDENTIALS**

### **Admin Account:**
- **Username:** `admin`
- **Password:** `admin123`
- **Role:** `Admin`

### **Cashier Account:**
- **Username:** `cashier`
- **Password:** `cashier123`
- **Role:** `Cashier`

### **CCTV Account:**
- **Username:** `cctv`
- **Password:** `cctv123`
- **Role:** `CCTV`

---

## 🔍 **Troubleshooting Steps**

### **Step 1: Use Latest Build**
1. Delete old installation
2. Install the new `rainbow-station-inc-pos-1.0.0 Setup.exe`
3. Run the application

### **Step 2: Check Database Location**
The database is now stored in:
```
Windows: C:\Users\<USER>\AppData\Roaming\rainbow-station-inc-pos\pos_system.db
```

### **Step 3: If Login Still Fails**

#### **Option A: Check Console Logs**
1. Press `Ctrl + Shift + I` to open Developer Tools
2. Go to Console tab
3. Look for error messages
4. Check for database initialization messages

#### **Option B: Manual Database Reset**
1. Close the application
2. Navigate to: `%APPDATA%\rainbow-station-inc-pos\`
3. Delete `pos_system.db` file
4. Restart application (will recreate database)

#### **Option C: Run Database Diagnostic**
1. Copy `debug-database.js` to application folder
2. Run: `electron debug-database.js`
3. Check output for database status

### **Step 4: Verify Application Startup**
Look for these console messages:
```
🔧 Initializing database...
Database path: [path to database]
Connected to SQLite database
✅ Database initialized successfully
Default admin user created (username: admin, password: admin123)
```

---

## 🐛 **Common Issues & Solutions**

### **Issue: "Database not ready" Error**
**Solution:** Wait 2-3 seconds after app starts, then try login

### **Issue: Login Form Not Responding**
**Solution:** 
1. Press `F5` to refresh
2. Or restart application

### **Issue: "Invalid credentials" with Correct Password**
**Solution:**
1. Ensure correct role is selected
2. Check caps lock
3. Try admin/admin123 with Admin role

### **Issue: Application Won't Start**
**Solution:**
1. Run as Administrator
2. Check Windows Defender/Antivirus
3. Reinstall application

---

## 📋 **Verification Checklist**

- [ ] Application starts without errors
- [ ] Database file created in user data folder
- [ ] Console shows "Database initialized successfully"
- [ ] Login form is responsive
- [ ] Can login with admin/admin123
- [ ] Redirects to appropriate interface after login

---

## 🆘 **Emergency Recovery**

If nothing works:

1. **Complete Reset:**
   ```
   1. Uninstall application
   2. Delete %APPDATA%\rainbow-station-inc-pos folder
   3. Reinstall application
   4. Use admin/admin123 credentials
   ```

2. **Contact Support:**
   - Provide console error messages
   - Include database diagnostic output
   - Specify Windows version and user permissions

---

## 📞 **Support Information**

**Default Credentials:** admin / admin123 (Admin role)
**Database Location:** %APPDATA%\rainbow-station-inc-pos\
**Log Location:** Developer Tools > Console
