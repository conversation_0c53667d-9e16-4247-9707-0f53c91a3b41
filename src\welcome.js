const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Navigate to login page
function goToLogin() {
    console.log('Navigating to login page...');
    ipcRenderer.invoke('navigate-to', 'login');
}

// Window control functions
function closeApp() {
    ipcRenderer.invoke('close-app');
}

function minimizeApp() {
    ipcRenderer.invoke('minimize-app');
}

// Initialize welcome page
document.addEventListener('DOMContentLoaded', () => {
    console.log('Welcome page loaded');
    
    // Add keyboard shortcut for Enter key to go to login
    document.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            goToLogin();
        }
    });
    
    // Focus the login button for immediate keyboard access
    const loginButton = document.querySelector('.login-button');
    if (loginButton) {
        loginButton.focus();
    }
});

// Add some interactive effects
document.addEventListener('DOMContentLoaded', () => {
    // Add click effect to particles
    const particles = document.querySelectorAll('.particle');
    particles.forEach(particle => {
        particle.addEventListener('click', () => {
            particle.style.animation = 'none';
            setTimeout(() => {
                particle.style.animation = 'float 6s ease-in-out infinite';
            }, 100);
        });
    });
    
    // Add mouse move effect for subtle interactivity
    document.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        particles.forEach((particle, index) => {
            const speed = (index + 1) * 0.5;
            const x = mouseX * speed;
            const y = mouseY * speed;
            
            particle.style.transform = `translate(${x}px, ${y}px)`;
        });
    });
});
