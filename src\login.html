<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rainbow Station Inc Point of Sale System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Window Controls */
        .window-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .control-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            background: rgba(0, 255, 0, 0.1);
            color: #00ff00;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        .control-btn:hover {
            background: rgba(0, 255, 0, 0.2);
            transform: scale(1.1);
        }

        .minimize-btn:hover {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border-color: #ffc107;
        }

        .close-btn:hover {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border-color: #dc3545;
        }

        /* Main Container */
        .main-container {
            display: flex;
            height: 100vh;
            width: 100%;
        }

        /* Left Side - Login Form */
        .login-side {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            background: linear-gradient(135deg, #000000 0%, #001100 100%);
            position: relative;
        }

        .login-side::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .login-container {
            background: rgba(17, 17, 17, 0.9);
            padding: 50px 40px;
            border-radius: 20px;
            border: 2px solid rgba(0, 255, 0, 0.3);
            box-shadow:
                0 0 30px rgba(0, 255, 0, 0.2),
                inset 0 0 20px rgba(0, 255, 0, 0.05);
            width: 100%;
            max-width: 450px;
            text-align: center;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        /* Right Side - Image */
        .image-side {
            flex: 1;
            background: linear-gradient(45deg, #001100 0%, #002200 50%, #000000 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .image-side::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(0, 255, 0, 0.1) 0%, transparent 70%),
                linear-gradient(45deg, transparent 30%, rgba(0, 255, 0, 0.05) 50%, transparent 70%);
        }

        .pos-image {
            width: 80%;
            height: 80%;
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid rgba(0, 255, 0, 0.3);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            text-align: center;
            position: relative;
            z-index: 1;
            backdrop-filter: blur(5px);
        }

        .login-title {
            font-size: 32px;
            margin-bottom: 40px;
            color: #00ff00;
            text-shadow: 0 0 20px rgba(0, 255, 0, 0.6);
            font-weight: 300;
            line-height: 1.2;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            from {
                text-shadow: 0 0 20px rgba(0, 255, 0, 0.6);
            }
            to {
                text-shadow: 0 0 30px rgba(0, 255, 0, 0.9), 0 0 40px rgba(0, 255, 0, 0.3);
            }
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #00ff00;
            font-weight: 500;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        input[type="text"],
        input[type="password"],
        select {
            width: 100%;
            padding: 15px 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(0, 255, 0, 0.3);
            border-radius: 10px;
            color: #00ff00;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        input[type="text"]:focus,
        input[type="password"]:focus,
        select:focus {
            outline: none;
            border-color: #00ff00;
            box-shadow:
                0 0 20px rgba(0, 255, 0, 0.4),
                inset 0 0 10px rgba(0, 255, 0, 0.1);
            background: rgba(0, 0, 0, 0.9);
        }

        input[type="text"]::placeholder,
        input[type="password"]::placeholder {
            color: rgba(0, 255, 0, 0.5);
        }

        select option {
            background-color: #000000;
            color: #00ff00;
            padding: 10px;
        }

        select option:disabled {
            color: #666666;
        }

        .login-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(45deg, #00ff00, #00cc00);
            color: #000000;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:hover {
            background: linear-gradient(45deg, #00cc00, #00aa00);
            box-shadow: 0 0 25px rgba(0, 255, 0, 0.6);
            transform: translateY(-2px);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .error-message {
            color: #ff4444;
            margin-top: 15px;
            font-size: 14px;
            text-align: center;
            padding: 10px;
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid rgba(255, 68, 68, 0.3);
            border-radius: 8px;
            animation: errorShake 0.5s ease-in-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
            }

            .image-side {
                order: -1;
                flex: 0 0 200px;
            }

            .login-side {
                flex: 1;
                padding: 20px;
            }

            .login-container {
                max-width: 400px;
                padding: 40px 30px;
            }

            .login-title {
                font-size: 28px;
                margin-bottom: 30px;
            }
        }

        @media (max-width: 768px) {
            .window-controls {
                top: 10px;
                right: 10px;
            }

            .control-btn {
                width: 25px;
                height: 25px;
                font-size: 14px;
            }

            .image-side {
                flex: 0 0 150px;
            }

            .login-container {
                max-width: 350px;
                padding: 30px 25px;
            }

            .login-title {
                font-size: 24px;
                margin-bottom: 25px;
            }

            input[type="text"],
            input[type="password"],
            select {
                padding: 12px 15px;
                font-size: 15px;
            }

            .login-button {
                padding: 15px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                flex-direction: column;
            }

            .image-side {
                display: none;
            }

            .login-side {
                padding: 15px;
            }

            .login-container {
                max-width: 100%;
                padding: 25px 20px;
                margin: 0 10px;
            }

            .login-title {
                font-size: 20px;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Window Controls -->
    <div class="window-controls">
        <button class="control-btn minimize-btn" onclick="minimizeApp()" title="Minimize">−</button>
        <button class="control-btn close-btn" onclick="closeApp()" title="Close">×</button>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Left Side - Login Form -->
        <div class="login-side">
            <div class="login-container">
                <h1 class="login-title">Rainbow Station Inc<br>Point of Sale System</h1>

                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" placeholder="Enter your username" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                    </div>

                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="Admin">Admin</option>
                            <option value="Cashier">Cashier</option>
                            <option value="CCTV">CCTV</option>
                        </select>
                    </div>

                    <button type="submit" class="login-button">Login</button>

                    <div id="errorMessage" class="error-message" style="display: none;"></div>
                </form>
            </div>
        </div>

        <!-- Right Side - Image -->
        <div class="image-side">
            <div class="pos-image">
                <div>
                    <h2 style="margin-bottom: 20px; font-size: 28px;">🏪</h2>
                    <h3 style="margin-bottom: 10px;">Modern POS System</h3>
                    <p style="opacity: 0.7; font-size: 16px;">Secure • Fast • Reliable</p>
                </div>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
</body>
</html>
