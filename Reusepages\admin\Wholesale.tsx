import { Button } from "@/components/ui/button"

export default function Wholesale() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Wholesale Management</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white border-2 border-green-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-green-600 mb-2">Active Wholesale Customers</h3>
          <p className="text-3xl font-black text-gray-800">24</p>
          <p className="text-sm text-gray-600">+3 this month</p>
        </div>
        <div className="bg-white border-2 border-blue-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-blue-600 mb-2">Pending Orders</h3>
          <p className="text-3xl font-black text-gray-800">8</p>
          <p className="text-sm text-gray-600">2 urgent</p>
        </div>
        <div className="bg-white border-2 border-yellow-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-yellow-600 mb-2">Monthly Revenue</h3>
          <p className="text-3xl font-black text-gray-800">$45,230</p>
          <p className="text-sm text-gray-600">+15% from last month</p>
        </div>
        <div className="bg-white border-2 border-purple-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-purple-600 mb-2">Average Order</h3>
          <p className="text-3xl font-black text-gray-800">$1,885</p>
          <p className="text-sm text-gray-600">+8% from last month</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Wholesale Customers</h3>
          <div className="space-y-3 mb-4">
            <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
              <div>
                <span className="text-green-700 font-medium">ABC Retail Store</span>
                <div className="text-sm text-gray-500">Last order: 2 days ago</div>
              </div>
              <span className="text-green-600 font-bold">$2,450</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
              <div>
                <span className="text-blue-700 font-medium">XYZ Convenience</span>
                <div className="text-sm text-gray-500">Last order: 1 week ago</div>
              </div>
              <span className="text-blue-600 font-bold">$1,890</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
              <div>
                <span className="text-yellow-700 font-medium">Quick Mart Chain</span>
                <div className="text-sm text-gray-500">Last order: 3 days ago</div>
              </div>
              <span className="text-yellow-600 font-bold">$3,200</span>
            </div>
          </div>
          <Button className="bg-green-600 hover:bg-green-700 text-white font-bold w-full">
            View All Customers
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Recent Wholesale Orders</h3>
          <div className="space-y-3 mb-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Order #WS-001</span>
                <div className="text-sm text-gray-500">ABC Retail Store</div>
              </div>
              <div className="text-right">
                <div className="text-green-600 font-bold">$2,450</div>
                <div className="text-xs text-green-600">Completed</div>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Order #WS-002</span>
                <div className="text-sm text-gray-500">Quick Mart Chain</div>
              </div>
              <div className="text-right">
                <div className="text-yellow-600 font-bold">$3,200</div>
                <div className="text-xs text-yellow-600">Processing</div>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Order #WS-003</span>
                <div className="text-sm text-gray-500">XYZ Convenience</div>
              </div>
              <div className="text-right">
                <div className="text-blue-600 font-bold">$1,890</div>
                <div className="text-xs text-blue-600">Pending</div>
              </div>
            </div>
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold w-full">
            View All Orders
          </Button>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Wholesale Management Tools</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button className="bg-green-600 hover:bg-green-700 text-white font-bold h-16 flex flex-col items-center justify-center">
            <span className="text-lg">📋</span>
            <span>Create Order</span>
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold h-16 flex flex-col items-center justify-center">
            <span className="text-lg">👥</span>
            <span>Manage Customers</span>
          </Button>
          <Button className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold h-16 flex flex-col items-center justify-center">
            <span className="text-lg">💰</span>
            <span>Pricing Tiers</span>
          </Button>
          <Button className="bg-purple-600 hover:bg-purple-700 text-white font-bold h-16 flex flex-col items-center justify-center">
            <span className="text-lg">📊</span>
            <span>Analytics</span>
          </Button>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Wholesale Pricing Tiers</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b-2 border-gray-200">
                <th className="text-gray-700 p-3 font-bold">Tier Level</th>
                <th className="text-gray-700 p-3 font-bold">Minimum Order</th>
                <th className="text-gray-700 p-3 font-bold">Discount %</th>
                <th className="text-gray-700 p-3 font-bold">Customers</th>
                <th className="text-gray-700 p-3 font-bold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-bold">
                    Bronze
                  </span>
                </td>
                <td className="p-3 text-gray-800">$500</td>
                <td className="p-3 text-green-600 font-bold">5%</td>
                <td className="p-3 text-gray-600">8 customers</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-bold">
                    Silver
                  </span>
                </td>
                <td className="p-3 text-gray-800">$1,000</td>
                <td className="p-3 text-blue-600 font-bold">10%</td>
                <td className="p-3 text-gray-600">12 customers</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-bold">
                    Gold
                  </span>
                </td>
                <td className="p-3 text-gray-800">$2,500</td>
                <td className="p-3 text-yellow-600 font-bold">15%</td>
                <td className="p-3 text-gray-600">4 customers</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
