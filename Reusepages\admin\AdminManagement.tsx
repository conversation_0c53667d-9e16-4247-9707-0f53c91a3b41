import { Button } from "@/components/ui/button"

export default function AdminManagement() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Admin Management</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white border-2 border-green-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-green-600 mb-2">Total Users</h3>
          <p className="text-3xl font-black text-gray-800">15</p>
          <p className="text-sm text-gray-600">3 admins, 12 staff</p>
        </div>
        <div className="bg-white border-2 border-blue-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-blue-600 mb-2">Active Sessions</h3>
          <p className="text-3xl font-black text-gray-800">8</p>
          <p className="text-sm text-gray-600">Currently online</p>
        </div>
        <div className="bg-white border-2 border-yellow-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-yellow-600 mb-2">System Health</h3>
          <p className="text-3xl font-black text-green-600">98%</p>
          <p className="text-sm text-gray-600">All systems operational</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">User Management</h3>
          <div className="space-y-4 mb-4">
            <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
              <div>
                <span className="text-green-700 font-medium">Simon (Admin)</span>
                <div className="text-sm text-gray-500">Last login: 2 hours ago</div>
              </div>
              <div className="flex gap-2">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                  Edit
                </Button>
                <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                  Disable
                </Button>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
              <div>
                <span className="text-blue-700 font-medium">John (Manager)</span>
                <div className="text-sm text-gray-500">Last login: 1 day ago</div>
              </div>
              <div className="flex gap-2">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                  Edit
                </Button>
                <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                  Disable
                </Button>
              </div>
            </div>
            <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
              <div>
                <span className="text-yellow-700 font-medium">Sarah (Cashier)</span>
                <div className="text-sm text-gray-500">Last login: 3 hours ago</div>
              </div>
              <div className="flex gap-2">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                  Edit
                </Button>
                <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                  Disable
                </Button>
              </div>
            </div>
          </div>
          <Button className="bg-green-600 hover:bg-green-700 text-white font-bold w-full">
            Add New User
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">System Settings</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Backup Settings</span>
                <div className="text-sm text-gray-500">Auto backup every 24 hours</div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                Configure
              </Button>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Security Settings</span>
                <div className="text-sm text-gray-500">Password policy, 2FA settings</div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                Configure
              </Button>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Email Settings</span>
                <div className="text-sm text-gray-500">SMTP configuration</div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                Configure
              </Button>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 border border-gray-200 rounded">
              <div>
                <span className="text-gray-700 font-medium">Tax Settings</span>
                <div className="text-sm text-gray-500">Tax rates and categories</div>
              </div>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                Configure
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Role & Permissions</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b-2 border-gray-200">
                <th className="text-gray-700 p-3 font-bold">Role</th>
                <th className="text-gray-700 p-3 font-bold">Users</th>
                <th className="text-gray-700 p-3 font-bold">Permissions</th>
                <th className="text-gray-700 p-3 font-bold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-bold">
                    Super Admin
                  </span>
                </td>
                <td className="p-3 text-gray-800">1 user</td>
                <td className="p-3 text-gray-600">Full system access</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit Permissions
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-bold">
                    Admin
                  </span>
                </td>
                <td className="p-3 text-gray-800">2 users</td>
                <td className="p-3 text-gray-600">Admin panel, reports, user management</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit Permissions
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-bold">
                    Manager
                  </span>
                </td>
                <td className="p-3 text-gray-800">3 users</td>
                <td className="p-3 text-gray-600">POS, inventory, reports</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit Permissions
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-bold">
                    Cashier
                  </span>
                </td>
                <td className="p-3 text-gray-800">9 users</td>
                <td className="p-3 text-gray-600">POS operations only</td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    Edit Permissions
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <h3 className="text-xl font-bold text-gray-800 mb-4">System Logs</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded">
            <div>
              <span className="text-green-700 font-medium">User Login</span>
              <div className="text-sm text-gray-500">Simon logged in from 192.168.1.100</div>
            </div>
            <span className="text-gray-500 text-sm">2 hours ago</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-blue-50 border border-blue-200 rounded">
            <div>
              <span className="text-blue-700 font-medium">System Backup</span>
              <div className="text-sm text-gray-500">Automatic backup completed successfully</div>
            </div>
            <span className="text-gray-500 text-sm">6 hours ago</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-yellow-50 border border-yellow-200 rounded">
            <div>
              <span className="text-yellow-700 font-medium">Settings Changed</span>
              <div className="text-sm text-gray-500">Tax rate updated by Admin</div>
            </div>
            <span className="text-gray-500 text-sm">1 day ago</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-red-50 border border-red-200 rounded">
            <div>
              <span className="text-red-700 font-medium">Failed Login Attempt</span>
              <div className="text-sm text-gray-500">Multiple failed attempts from *************</div>
            </div>
            <span className="text-gray-500 text-sm">2 days ago</span>
          </div>
        </div>
        <div className="mt-4">
          <Button className="bg-gray-600 hover:bg-gray-700 text-white font-bold w-full">
            View Full System Logs
          </Button>
        </div>
      </div>
    </div>
  )
}
