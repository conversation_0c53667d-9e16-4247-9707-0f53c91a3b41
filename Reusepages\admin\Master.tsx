import { Button } from "@/components/ui/button"

export default function Master() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Master Data Management</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-green-600 mb-4">Product Management</h3>
          <p className="text-gray-600 mb-4">Manage products, categories, and pricing</p>
          <Button className="bg-green-600 hover:bg-green-700 text-white font-bold shadow-sm w-full">
            Manage Products
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-blue-600 mb-4">Category Management</h3>
          <p className="text-gray-600 mb-4">Organize product categories and subcategories</p>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold shadow-sm w-full">
            Manage Categories
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-yellow-600 mb-4">Supplier Management</h3>
          <p className="text-gray-600 mb-4">Manage suppliers and vendor information</p>
          <Button className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold shadow-sm w-full">
            Manage Suppliers
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-purple-600 mb-4">Inventory Management</h3>
          <p className="text-gray-600 mb-4">Track stock levels and inventory movements</p>
          <Button className="bg-purple-600 hover:bg-purple-700 text-white font-bold shadow-sm w-full">
            Manage Inventory
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-indigo-600 mb-4">Price Management</h3>
          <p className="text-gray-600 mb-4">Set and update product pricing rules</p>
          <Button className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold shadow-sm w-full">
            Manage Pricing
          </Button>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-pink-600 mb-4">Tax Management</h3>
          <p className="text-gray-600 mb-4">Configure tax rates and tax categories</p>
          <Button className="bg-pink-600 hover:bg-pink-700 text-white font-bold shadow-sm w-full">
            Manage Taxes
          </Button>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Recent Master Data Changes</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-gray-700 p-3 font-bold">Type</th>
                <th className="text-gray-700 p-3 font-bold">Item</th>
                <th className="text-gray-700 p-3 font-bold">Action</th>
                <th className="text-gray-700 p-3 font-bold">Date</th>
                <th className="text-gray-700 p-3 font-bold">User</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-green-600 font-medium">Product</td>
                <td className="p-3 text-gray-800">Glass Water Pipe</td>
                <td className="p-3 text-blue-600">Updated Price</td>
                <td className="p-3 text-gray-600">2024-01-15</td>
                <td className="p-3 text-gray-600">Simon</td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">Category</td>
                <td className="p-3 text-gray-800">A. Pipes</td>
                <td className="p-3 text-green-600">Added</td>
                <td className="p-3 text-gray-600">2024-01-14</td>
                <td className="p-3 text-gray-600">Admin</td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-yellow-600 font-medium">Supplier</td>
                <td className="p-3 text-gray-800">ABC Wholesale</td>
                <td className="p-3 text-purple-600">Updated Contact</td>
                <td className="p-3 text-gray-600">2024-01-13</td>
                <td className="p-3 text-gray-600">Manager</td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-purple-600 font-medium">Inventory</td>
                <td className="p-3 text-gray-800">Energy Drinks</td>
                <td className="p-3 text-orange-600">Stock Adjustment</td>
                <td className="p-3 text-gray-600">2024-01-12</td>
                <td className="p-3 text-gray-600">Simon</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
