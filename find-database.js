const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');

// Initialize app to get user data path
app.whenReady().then(() => {
  console.log('🔍 Database Location Finder');
  console.log('===========================');
  
  const userDataPath = app.getPath('userData');
  const dbPath = path.join(userDataPath, 'pos_system.db');
  
  console.log('📁 Application Name:', app.getName());
  console.log('📂 User Data Directory:', userDataPath);
  console.log('🗄️ Database File Path:', dbPath);
  console.log('');
  
  // Check if directory exists
  if (fs.existsSync(userDataPath)) {
    console.log('✅ User data directory EXISTS');
    
    // List all files in the directory
    try {
      const files = fs.readdirSync(userDataPath);
      console.log('📋 Files in user data directory:');
      files.forEach(file => {
        const filePath = path.join(userDataPath, file);
        const stats = fs.statSync(filePath);
        const size = stats.isFile() ? `(${stats.size} bytes)` : '(directory)';
        console.log(`   - ${file} ${size}`);
      });
    } catch (err) {
      console.error('❌ Error reading directory:', err.message);
    }
  } else {
    console.log('❌ User data directory does NOT exist');
  }
  
  console.log('');
  
  // Check if database file exists
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    console.log('✅ Database file EXISTS');
    console.log('📊 Database file size:', stats.size, 'bytes');
    console.log('📅 Created:', stats.birthtime);
    console.log('📅 Modified:', stats.mtime);
  } else {
    console.log('❌ Database file does NOT exist');
  }
  
  console.log('');
  console.log('🔗 Quick Access Paths:');
  console.log('   Windows Explorer: file:///' + userDataPath.replace(/\\/g, '/'));
  console.log('   Command Prompt: explorer "' + userDataPath + '"');
  console.log('   PowerShell: explorer "' + userDataPath + '"');
  
  // Try to open the folder in Windows Explorer
  console.log('');
  console.log('🚀 Opening folder in Windows Explorer...');
  exec(`explorer "${userDataPath}"`, (error) => {
    if (error) {
      console.error('❌ Could not open folder:', error.message);
    } else {
      console.log('✅ Folder opened in Windows Explorer');
    }
    
    setTimeout(() => {
      app.quit();
    }, 2000);
  });
});

// Handle app events
app.on('window-all-closed', () => {
  app.quit();
});
