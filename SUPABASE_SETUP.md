# Supabase Cloud Sync Setup

## Overview
Your Rainbow Station Inc POS system is now connected to Supabase cloud database for automatic data synchronization.

## Project Details
- **Project Name:** Rainbow Station Inc POS
- **Project ID:** ctxqgwphsimygwdshnun
- **Region:** ap-southeast-1 (Asia Pacific - Singapore)
- **Database:** PostgreSQL 17.4.1
- **Monthly Cost:** $10

## Installation

### 1. Install Dependencies
Run the installation script:
```bash
# On Windows
install-supabase.bat

# Or manually
npm install @supabase/supabase-js@^2.39.0
```

### 2. Start the Application
```bash
npm start
```

## Features

### Automatic Sync
- **Frequency:** Every 10 minutes
- **Tables Synced:** All 14 tables (users, locations, products, sales, etc.)
- **Method:** Full table replacement (local data overwrites cloud data)

### Manual Sync
- Available in Admin Dashboard
- Use when automatic sync fails
- Provides real-time feedback

### Sync Status Monitoring
- Real-time status display in dashboard
- Last sync timestamp
- Error reporting
- Auto-sync enable/disable toggle

## How It Works

1. **Local Database:** SQLite database stores all POS data locally
2. **Cloud Database:** Supabase PostgreSQL mirrors the local data
3. **Sync Process:** Every 10 minutes, all local data is pushed to cloud
4. **Data Safety:** Local database remains primary, cloud is backup/sync

## Sync Process Details

### Tables Synchronized:
1. `users` - User accounts and permissions
2. `locations` - Store locations
3. `user_permissions` - Role-based permissions
4. `categories` - Product categories
5. `suppliers` - Supplier information
6. `products` - Product catalog
7. `location_stocks` - Inventory per location
8. `tickets` - Theater tickets
9. `banned_tickets` - Banned/refunded tickets
10. `sales` - Completed sales transactions
11. `sales_items` - Individual sale items
12. `draft_sales` - Pending sales
13. `draft_sales_items` - Pending sale items
14. `shifts` - Employee shift data

### Data Transformation:
- SQLite INTEGER IDs → PostgreSQL BIGSERIAL
- SQLite DATETIME → PostgreSQL TIMESTAMPTZ
- SQLite boolean (0/1) → PostgreSQL boolean (true/false)

## Dashboard Controls

### Sync Status Section
Located in the Admin Dashboard, shows:
- Current sync status (Ready/Syncing/Success/Error)
- Last successful sync time
- Auto-sync status (Enabled/Disabled)
- Manual sync button
- Auto-sync toggle button

### Status Indicators:
- 🟢 **Ready/Success:** System ready or last sync successful
- 🟡 **Syncing:** Data sync in progress
- 🔴 **Error:** Sync failed, check error details
- ⚫ **Not Connected:** Supabase connection issue

## Troubleshooting

### Common Issues:

1. **"Supabase sync not initialized"**
   - Restart the application
   - Check internet connection

2. **"Connection failed"**
   - Verify internet connectivity
   - Check Supabase service status

3. **"Sync failed"**
   - Try manual sync
   - Check error details in dashboard
   - Restart application if needed

### Manual Recovery:
If automatic sync stops working:
1. Use the "Manual Sync" button in dashboard
2. Toggle auto-sync off and on again
3. Restart the application

## Security Notes

- API keys are embedded in the application
- Data is transmitted over HTTPS
- Local database remains primary data source
- Cloud database is for backup and synchronization only

## Support

For technical support or issues:
1. Check the dashboard sync status
2. Review error messages
3. Try manual sync first
4. Restart application if needed

---

**Important:** The local SQLite database remains your primary data source. The cloud sync is for backup and data redundancy purposes.
