Complete Electron Login System Guide
1. Required Dependencies
First, install the necessary packages:
bashnpm install sqlite3 bcryptjs
2. Main Process Setup (index.js)
Replace your main index.js file with this code:
javascriptconst { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const Database = require('./database');

let mainWindow;
let db;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    titleBarStyle: 'hidden', // Remove default toolbar
    frame: false // Remove window frame for modern look
  });

  // Load the login page
  mainWindow.loadFile('login.html');

  // Initialize database
  db = new Database();
  db.init();
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for login
ipcMain.handle('login', async (event, credentials) => {
  try {
    const user = await db.authenticateUser(credentials.username, credentials.password, credentials.role);
    if (user) {
      // Load dashboard after successful login
      mainWindow.loadFile('dashboard.html');
      return { success: true, user };
    } else {
      return { success: false, message: 'Invalid credentials' };
    }
  } catch (error) {
    return { success: false, message: 'Database error' };
  }
});

// Handle window close
ipcMain.handle('close-app', () => {
  app.quit();
});
3. Database Service (database.js)
Create a new file database.js:
javascriptconst sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');

class Database {
  constructor() {
    this.db = null;
  }

  init() {
    // Create database file in app directory
    const dbPath = path.join(__dirname, 'pos_system.db');
    
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.createTables();
        this.createDefaultAdmin();
      }
    });
  }

  createTables() {
    const createUserTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    this.db.run(createUserTable, (err) => {
      if (err) {
        console.error('Error creating users table:', err);
      } else {
        console.log('Users table created successfully');
      }
    });
  }

  async createDefaultAdmin() {
    // Create default admin user
    const defaultUsername = 'admin';
    const defaultPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const insertAdmin = `
      INSERT OR IGNORE INTO users (username, password, role) 
      VALUES (?, ?, 'Admin')
    `;

    this.db.run(insertAdmin, [defaultUsername, hashedPassword], function(err) {
      if (err) {
        console.error('Error creating default admin:', err);
      } else {
        console.log('Default admin user created (username: admin, password: admin123)');
      }
    });
  }

  authenticateUser(username, password, role) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM users WHERE username = ? AND role = ?`;
      
      this.db.get(query, [username, role], async (err, user) => {
        if (err) {
          reject(err);
        } else if (user) {
          // Verify password
          const isValidPassword = await bcrypt.compare(password, user.password);
          if (isValidPassword) {
            resolve(user);
          } else {
            resolve(null);
          }
        } else {
          resolve(null);
        }
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = Database;
4. Login Page HTML (login.html)
Create login.html:
html<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rainbow Station Inc Point of Sale System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Arial', sans-serif;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .login-container {
            background-color: #111111;
            padding: 40px;
            border-radius: 10px;
            border: 2px solid #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            width: 400px;
            text-align: center;
        }

        .login-title {
            font-size: 24px;
            margin-bottom: 30px;
            color: #00ff00;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #00ff00;
            font-weight: bold;
        }

        input[type="text"],
        input[type="password"],
        select {
            width: 100%;
            padding: 12px;
            background-color: #000000;
            border: 2px solid #00ff00;
            border-radius: 5px;
            color: #00ff00;
            font-size: 16px;
        }

        input[type="text"]:focus,
        input[type="password"]:focus,
        select:focus {
            outline: none;
            border-color: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }

        select option {
            background-color: #000000;
            color: #00ff00;
        }

        select option:disabled {
            color: #666666;
        }

        .login-button {
            width: 100%;
            padding: 15px;
            background-color: #00ff00;
            color: #000000;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-button:hover {
            background-color: #00cc00;
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
        }

        .error-message {
            color: #ff0000;
            margin-top: 10px;
            font-size: 14px;
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #00ff00;
            font-size: 20px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .close-button:hover {
            color: #ff0000;
        }
    </style>
</head>
<body>
    <button class="close-button" onclick="closeApp()">×</button>
    
    <div class="login-container">
        <h1 class="login-title">Rainbow Station Inc<br>Point of Sale System</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="role">Role:</label>
                <select id="role" name="role" required>
                    <option value="">Select Role</option>
                    <option value="Admin">Admin</option>
                    <option value="Cashier" disabled>Cashier (Under Development)</option>
                    <option value="CCTV" disabled>CCTV (Under Development)</option>
                </select>
            </div>
            
            <button type="submit" class="login-button">Login</button>
            
            <div id="errorMessage" class="error-message"></div>
        </form>
    </div>

    <script src="login.js"></script>
</body>
</html>
5. Login JavaScript (login.js)
Create login.js:
javascriptconst { ipcRenderer } = require('electron');

document.getElementById('loginForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const role = document.getElementById('role').value;
    const errorDiv = document.getElementById('errorMessage');
    
    // Clear previous error
    errorDiv.textContent = '';
    
    // Validate inputs
    if (!username || !password || !role) {
        errorDiv.textContent = 'Please fill in all fields';
        return;
    }
    
    // Only allow Admin role
    if (role !== 'Admin') {
        errorDiv.textContent = 'Only Admin role is available';
        return;
    }
    
    try {
        // Send login request to main process
        const result = await ipcRenderer.invoke('login', {
            username: username,
            password: password,
            role: role
        });
        
        if (result.success) {
            // Login successful - main process will handle redirect
            console.log('Login successful');
        } else {
            errorDiv.textContent = result.message || 'Login failed';
        }
    } catch (error) {
        errorDiv.textContent = 'An error occurred during login';
        console.error('Login error:', error);
    }
});

// Close application
function closeApp() {
    ipcRenderer.invoke('close-app');
}

// Auto-focus username field
document.getElementById('username').focus();
6. Dashboard Page HTML (dashboard.html)
Create dashboard.html:
html<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Rainbow Station Inc POS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #000000;
            color: #00ff00;
            font-family: 'Arial', sans-serif;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .dashboard-container {
            text-align: center;
            padding: 50px;
            border: 2px solid #00ff00;
            border-radius: 10px;
            background-color: #111111;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }

        .welcome-message {
            font-size: 48px;
            color: #00ff00;
            text-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                text-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
            }
            to {
                text-shadow: 0 0 25px rgba(0, 255, 0, 1);
            }
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #00ff00;
            font-size: 20px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .close-button:hover {
            color: #ff0000;
        }
    </style>
</head>
<body>
    <button class="close-button" onclick="closeApp()">×</button>
    
    <div class="dashboard-container">
        <h1 class="welcome-message">Welcome to Rainbow Station Inc POS System</h1>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        function closeApp() {
            ipcRenderer.invoke('close-app');
        }
    </script>
</body>
</body>
</html>
7. Package.json Configuration
Make sure your package.json includes the necessary dependencies:
json{
  "name": "rainbow-station-pos",
  "version": "1.0.0",
  "description": "Rainbow Station Inc Point of Sale System",
  "main": "index.js",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev"
  },
  "dependencies": {
    "electron": "^latest",
    "sqlite3": "^5.1.6",
    "bcryptjs": "^2.4.3"
  }
}
8. Running the Application

Install dependencies:
bashnpm install

Start the application:
bashnpm start


9. Default Login Credentials
The system automatically creates a default admin user:

Username: admin
Password: admin123
Role: Admin

10. Key Features Implemented
✅ Complete black background with green text
✅ Modern UI design
✅ Removed default toolbar
✅ Custom window title
✅ Username, Password, and Role fields
✅ Three roles with Cashier and CCTV disabled
✅ SQLite database integration
✅ Password hashing with bcrypt
✅ User authentication
✅ Simple welcome dashboard
✅ Secure database connection
11. Security Notes

Passwords are hashed using bcrypt before storage
Only Admin role is functional as requested
Database file is created locally in the application directory
IPC communication is used for secure main-renderer process communication

12. Database Structure
The users table contains:

id (Primary Key)
username (Unique)
password (Hashed)
role (Admin/Cashier/CCTV)
created_at (Timestamp)

This complete system meets all your requirements and provides a solid foundation for your Electron POS application.