import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface SupplierData {
  name: string
  address1: string
  address2: string
  city: string
  state: string
  zipCode: string
  telephone: string
  fax: string
  email: string
  salesRep: string
  salesRepPhone: string
  retailWebsite: string
  wholesaleWebsite: string
  username: string
  password: string
}

interface SupplierListItem {
  supplierName: string
  telephone: string
  fax: string
  email: string
  salesRep: string
  repPhone: string
}

export default function SetUpSupplier() {
  const [formData, setFormData] = useState<SupplierData>({
    name: "",
    address1: "",
    address2: "",
    city: "",
    state: "",
    zipCode: "",
    telephone: "",
    fax: "",
    email: "",
    salesRep: "",
    salesRepPhone: "",
    retailWebsite: "",
    wholesaleWebsite: "",
    username: "",
    password: ""
  })

  const [suppliers] = useState<SupplierListItem[]>([
    {
      supplierName: "505 INC Distribution center",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "ALLEN BROTHERS",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "Baci",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "Bamboo Magic",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "BE WICKED!",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "Black Unicorn",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "BLUE OX DESIGNS LLC",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "Boy butter",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    },
    {
      supplierName: "Cents Price",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      repPhone: ""
    }
  ])

  const handleInputChange = (field: keyof SupplierData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    console.log("Saving supplier:", formData)
  }

  const handleClear = () => {
    setFormData({
      name: "",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zipCode: "",
      telephone: "",
      fax: "",
      email: "",
      salesRep: "",
      salesRepPhone: "",
      retailWebsite: "",
      wholesaleWebsite: "",
      username: "",
      password: ""
    })
  }

  const handleDelete = () => {
    console.log("Deleting supplier")
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Set Up Supplier</h2>

      {/* Top Action Buttons */}
      <div className="flex gap-3 mb-6">
        <Button
          onClick={handleSave}
          className="bg-green-600 hover:bg-green-700 text-white font-bold px-8 py-2"
        >
          SAVE
        </Button>
        <Button
          onClick={handleClear}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold px-8 py-2"
        >
          CLEAR
        </Button>
        <Button
          onClick={handleDelete}
          className="bg-red-600 hover:bg-red-700 text-white font-bold px-8 py-2"
        >
          DELETE
        </Button>
      </div>

      {/* Main Form */}
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg">
        <div className="p-6 space-y-6">
          {/* Name Row */}
          <div>
            <Label className="text-sm font-bold text-gray-700 mb-2 block">Name</Label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Enter supplier name"
            />
          </div>

          {/* Address Fields */}
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Address1</Label>
              <Input
                value={formData.address1}
                onChange={(e) => handleInputChange("address1", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter address line 1"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Address2</Label>
              <Input
                value={formData.address2}
                onChange={(e) => handleInputChange("address2", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter address line 2 (optional)"
              />
            </div>
          </div>

          {/* City, State, ZIP Row */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">City</Label>
              <Input
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter city"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">State</Label>
              <Input
                value={formData.state}
                onChange={(e) => handleInputChange("state", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter state"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">ZIP Code</Label>
              <Input
                value={formData.zipCode}
                onChange={(e) => handleInputChange("zipCode", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter ZIP code"
              />
            </div>
          </div>

          {/* Contact Information Row */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Telephone</Label>
              <Input
                type="tel"
                value={formData.telephone}
                onChange={(e) => handleInputChange("telephone", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter telephone"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Fax</Label>
              <Input
                type="tel"
                value={formData.fax}
                onChange={(e) => handleInputChange("fax", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter fax number"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Email</Label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter email address"
              />
            </div>
          </div>

          {/* Sales Rep Information Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Sale's Rep</Label>
              <Input
                value={formData.salesRep}
                onChange={(e) => handleInputChange("salesRep", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter sales representative name"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Sale's Rep Phone</Label>
              <Input
                type="tel"
                value={formData.salesRepPhone}
                onChange={(e) => handleInputChange("salesRepPhone", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter sales rep phone"
              />
            </div>
          </div>

          {/* Website Information Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Retail Website</Label>
              <Input
                type="url"
                value={formData.retailWebsite}
                onChange={(e) => handleInputChange("retailWebsite", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter retail website URL"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Wholesale Website</Label>
              <Input
                type="url"
                value={formData.wholesaleWebsite}
                onChange={(e) => handleInputChange("wholesaleWebsite", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter wholesale website URL"
              />
            </div>
          </div>

          {/* Login Credentials Row */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Username</Label>
              <Input
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter username"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Password</Label>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter password"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Supplier Directory Table */}
      <div className="mt-8 bg-white border border-gray-300 rounded-lg shadow-lg">
        <div className="p-6">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-blue-600 text-white">
                  <th className="text-left p-3 font-bold border-r border-blue-500">Supplier Name</th>
                  <th className="text-center p-3 font-bold border-r border-blue-500">Telephone</th>
                  <th className="text-center p-3 font-bold border-r border-blue-500">Fax</th>
                  <th className="text-center p-3 font-bold border-r border-blue-500">Email</th>
                  <th className="text-center p-3 font-bold border-r border-blue-500">Sale's Rep</th>
                  <th className="text-center p-3 font-bold">Rep Phone #</th>
                </tr>
              </thead>
              <tbody>
                {suppliers.map((supplier, index) => (
                  <tr
                    key={index}
                    className={`border-b border-gray-200 hover:bg-gray-50 ${
                      index === 0 ? 'bg-blue-100' : ''
                    }`}
                  >
                    <td className="p-3 font-medium text-gray-800 border-r border-gray-300">
                      {supplier.supplierName}
                    </td>
                    <td className="p-2 text-center border-r border-gray-300">
                      <Input
                        value={supplier.telephone}
                        className="w-full text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder=""
                      />
                    </td>
                    <td className="p-2 text-center border-r border-gray-300">
                      <Input
                        value={supplier.fax}
                        className="w-full text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder=""
                      />
                    </td>
                    <td className="p-2 text-center border-r border-gray-300">
                      <Input
                        type="email"
                        value={supplier.email}
                        className="w-full text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder=""
                      />
                    </td>
                    <td className="p-2 text-center border-r border-gray-300">
                      <Input
                        value={supplier.salesRep}
                        className="w-full text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder=""
                      />
                    </td>
                    <td className="p-2 text-center">
                      <Input
                        type="tel"
                        value={supplier.repPhone}
                        className="w-full text-center border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder=""
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
