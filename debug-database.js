const { app } = require('electron');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// Initialize app to get user data path
app.whenReady().then(() => {
  console.log('🔍 Database Diagnostic Tool');
  console.log('==========================');
  
  const userDataPath = app.getPath('userData');
  const dbPath = path.join(userDataPath, 'pos_system.db');
  
  console.log('📁 User Data Path:', userDataPath);
  console.log('🗄️ Database Path:', dbPath);
  console.log('📂 Directory exists:', fs.existsSync(userDataPath));
  console.log('🗃️ Database file exists:', fs.existsSync(dbPath));
  
  if (fs.existsSync(dbPath)) {
    console.log('📊 Database file size:', fs.statSync(dbPath).size, 'bytes');
  }
  
  // Try to connect to database
  const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('❌ Database connection error:', err.message);
    } else {
      console.log('✅ Database connection successful');
      
      // Check if users table exists and has data
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='users'", (err, row) => {
        if (err) {
          console.error('❌ Error checking users table:', err.message);
        } else if (row) {
          console.log('✅ Users table exists');
          
          // Count users
          db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
            if (err) {
              console.error('❌ Error counting users:', err.message);
            } else {
              console.log('👥 Total users in database:', row.count);
              
              // List all users
              db.all("SELECT id, username, role, status FROM users", (err, rows) => {
                if (err) {
                  console.error('❌ Error listing users:', err.message);
                } else {
                  console.log('📋 Users in database:');
                  rows.forEach(user => {
                    console.log(`   - ID: ${user.id}, Username: ${user.username}, Role: ${user.role}, Status: ${user.status}`);
                  });
                }
                
                db.close();
                app.quit();
              });
            }
          });
        } else {
          console.log('❌ Users table does not exist');
          db.close();
          app.quit();
        }
      });
    }
  });
});

// Handle app events
app.on('window-all-closed', () => {
  app.quit();
});
