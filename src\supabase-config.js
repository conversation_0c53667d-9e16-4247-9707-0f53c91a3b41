const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://ctxqgwphsimygwdshnun.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN0eHFnd3Boc2lteWd3ZHNobnVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzMjY0OTIsImV4cCI6MjA2ODkwMjQ5Mn0.Unj9DiBfri_7nzbkdp1JE13DklRm67iUrcNlBsF2lZU';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

class SupabaseSync {
  constructor(localDatabase) {
    this.localDb = localDatabase;
    this.supabase = supabase;
    this.syncInterval = null;
    this.isSyncing = false;
    this.lastSyncTime = null;
    this.syncStatus = 'idle'; // idle, syncing, success, error
    this.syncErrors = [];
  }

  // Start automatic sync every 10 minutes
  startAutoSync() {
    console.log('🔄 Starting automatic sync every 10 minutes...');
    
    // Clear any existing interval
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Set up 10-minute interval (600,000 milliseconds)
    this.syncInterval = setInterval(() => {
      this.syncAllData();
    }, 10 * 60 * 1000);

    // Also sync immediately when starting
    this.syncAllData();
  }

  // Stop automatic sync
  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ Automatic sync stopped');
    }
  }

  // Manual sync function
  async manualSync() {
    console.log('🔄 Manual sync triggered...');
    return await this.syncAllData();
  }

  // Main sync function
  async syncAllData() {
    if (this.isSyncing) {
      console.log('⚠️ Sync already in progress, skipping...');
      return { success: false, message: 'Sync already in progress' };
    }

    this.isSyncing = true;
    this.syncStatus = 'syncing';
    this.syncErrors = [];

    console.log('🚀 Starting data sync to Supabase...');

    try {
      // Sync tables in dependency order to avoid foreign key conflicts
      const syncResults = {};

      // Step 1: Sync tables with no dependencies
      console.log('🔄 Step 1: Syncing independent tables...');
      syncResults.locations = await this.syncTable('locations');
      syncResults.categories = await this.syncTable('categories');
      syncResults.suppliers = await this.syncTable('suppliers');

      // Step 2: Sync tables that depend on the above
      console.log('🔄 Step 2: Syncing user and product tables...');
      syncResults.users = await this.syncTable('users');
      syncResults.products = await this.syncTable('products');

      // Step 3: Sync tables that depend on users and products
      console.log('🔄 Step 3: Syncing dependent tables...');
      syncResults.user_permissions = await this.syncTable('user_permissions');
      syncResults.location_stocks = await this.syncTable('location_stocks');
      syncResults.tickets = await this.syncTable('tickets');
      syncResults.banned_tickets = await this.syncTable('banned_tickets');
      syncResults.shifts = await this.syncTable('shifts');

      // Step 4: Sync sales tables (depend on users and products)
      console.log('🔄 Step 4: Syncing sales tables...');
      syncResults.sales = await this.syncTable('sales');
      syncResults.draft_sales = await this.syncTable('draft_sales');

      // Step 5: Sync item tables that depend on sales (MUST be last)
      console.log('🔄 Step 5: Syncing sales item tables...');
      syncResults.sales_items = await this.syncTable('sales_items');
      syncResults.draft_sales_items = await this.syncTable('draft_sales_items');

      // Check if all syncs were successful
      const allSuccessful = Object.values(syncResults).every(result => result.success);
      
      if (allSuccessful) {
        this.syncStatus = 'success';
        this.lastSyncTime = new Date().toISOString();
        console.log('✅ All data synced successfully to Supabase');
        
        return {
          success: true,
          message: 'All data synced successfully',
          timestamp: this.lastSyncTime,
          results: syncResults
        };
      } else {
        this.syncStatus = 'error';
        const failedTables = Object.entries(syncResults)
          .filter(([table, result]) => !result.success)
          .map(([table, result]) => `${table}: ${result.error}`);
        
        this.syncErrors = failedTables;
        console.error('❌ Some tables failed to sync:', failedTables);
        
        return {
          success: false,
          message: 'Some tables failed to sync',
          errors: failedTables,
          results: syncResults
        };
      }

    } catch (error) {
      this.syncStatus = 'error';
      this.syncErrors = [error.message];
      console.error('❌ Sync failed:', error);
      
      return {
        success: false,
        message: 'Sync failed',
        error: error.message
      };
    } finally {
      this.isSyncing = false;
    }
  }

  // Sync individual table
  async syncTable(tableName) {
    try {
      console.log(`📊 Syncing table: ${tableName}`);

      // Get all data from local table
      const localData = await this.getLocalTableData(tableName);

      if (!localData || localData.length === 0) {
        console.log(`📭 No data found in local table: ${tableName}`);
        return { success: true, message: 'No data to sync', count: 0 };
      }

      // Transform data for Supabase (preserve original IDs)
      let transformedData = this.transformDataForSupabase(localData, tableName);

      // Validate foreign key references for sales_items and draft_sales_items
      if (tableName === 'sales_items' || tableName === 'draft_sales_items') {
        transformedData = await this.validateSalesItemReferences(transformedData, tableName);
      }

      // Clear existing data in Supabase table
      const { error: deleteError } = await this.supabase
        .from(tableName)
        .delete()
        .neq('id', 0); // Delete all records

      if (deleteError) {
        throw new Error(`Failed to clear ${tableName}: ${deleteError.message}`);
      }

      // Insert new data using upsert to handle ID conflicts
      const batchSize = 100; // Process in batches
      let insertedCount = 0;

      for (let i = 0; i < transformedData.length; i += batchSize) {
        const batch = transformedData.slice(i, i + batchSize);

        // Use upsert with onConflict to handle ID preservation
        const { error: insertError } = await this.supabase
          .from(tableName)
          .upsert(batch, {
            onConflict: 'id',
            ignoreDuplicates: false
          });

        if (insertError) {
          throw new Error(`Failed to upsert batch into ${tableName}: ${insertError.message}`);
        }

        insertedCount += batch.length;
        console.log(`📊 Upserted ${insertedCount}/${transformedData.length} records into ${tableName}`);
      }

      console.log(`✅ Successfully synced ${transformedData.length} records to ${tableName}`);
      return {
        success: true,
        message: `Synced ${transformedData.length} records`,
        count: transformedData.length
      };

    } catch (error) {
      console.error(`❌ Failed to sync table ${tableName}:`, error);
      return {
        success: false,
        error: error.message,
        table: tableName
      };
    }
  }

  // Get data from local SQLite table
  async getLocalTableData(tableName) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${tableName}`;
      
      this.localDb.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Transform data for Supabase compatibility
  transformDataForSupabase(data, tableName) {
    return data.map(row => {
      const transformedRow = { ...row };

      // Keep the original ID to maintain foreign key relationships
      // Don't delete the ID - we need it for relationships

      // Handle boolean fields (SQLite stores as 0/1, PostgreSQL needs true/false)
      if (tableName === 'banned_tickets' && 'is_refunded' in transformedRow) {
        transformedRow.is_refunded = transformedRow.is_refunded === 1;
      }

      // Handle location deli field
      if (tableName === 'locations' && 'deli' in transformedRow) {
        transformedRow.deli = transformedRow.deli || 0;
      }

      // Handle user permissions boolean-like fields
      if (tableName === 'user_permissions') {
        transformedRow.can_view = transformedRow.can_view || 0;
        transformedRow.can_edit = transformedRow.can_edit || 0;
        transformedRow.can_delete = transformedRow.can_delete || 0;
      }

      // Handle product boolean-like fields
      if (tableName === 'products') {
        transformedRow.special_discount = transformedRow.special_discount || 0;
        transformedRow.priority = transformedRow.priority || 0;
        transformedRow.image_confirm = transformedRow.image_confirm || 0;
        transformedRow.non_scanable = transformedRow.non_scanable || 0;
        transformedRow.daily_item = transformedRow.daily_item || 0;
      }

      // Handle foreign key references - ensure they are valid or null
      // For optional foreign keys, set to null if undefined/empty
      if (tableName === 'users') {
        if (!transformedRow.location_id || transformedRow.location_id === 0) {
          transformedRow.location_id = null;
        }
      }

      if (tableName === 'sales') {
        if (!transformedRow.user_id || transformedRow.user_id === 0) {
          transformedRow.user_id = null;
        }
        if (!transformedRow.location_id || transformedRow.location_id === 0) {
          transformedRow.location_id = null;
        }
      }

      if (tableName === 'sales_items') {
        if (!transformedRow.product_id || transformedRow.product_id === 0) {
          transformedRow.product_id = null;
        }
      }

      if (tableName === 'draft_sales_items') {
        if (!transformedRow.product_id || transformedRow.product_id === 0) {
          transformedRow.product_id = null;
        }
      }

      if (tableName === 'location_stocks') {
        if (!transformedRow.product_id || transformedRow.product_id === 0) {
          transformedRow.product_id = null;
        }
      }

      // Handle string-based foreign keys for sales_items and draft_sales_items
      if (tableName === 'sales_items') {
        if (!transformedRow.sale_id || transformedRow.sale_id === '') {
          transformedRow.sale_id = 'INVALID_SALE_ID'; // Will be filtered out later
        }
        if (!transformedRow.product_id || transformedRow.product_id === 0) {
          transformedRow.product_id = null;
        }
      }

      if (tableName === 'draft_sales_items') {
        if (!transformedRow.draft_sale_id || transformedRow.draft_sale_id === '') {
          transformedRow.draft_sale_id = 'INVALID_DRAFT_SALE_ID'; // Will be filtered out later
        }
        if (!transformedRow.product_id || transformedRow.product_id === 0) {
          transformedRow.product_id = null;
        }
      }

      return transformedRow;
    });
  }

  // Validate foreign key references for sales items
  async validateSalesItemReferences(data, tableName) {
    try {
      let validIds = new Set();

      if (tableName === 'sales_items') {
        // Get all existing sale_ids from Supabase
        const { data: existingSales, error } = await this.supabase
          .from('sales')
          .select('sale_id');

        if (!error && existingSales) {
          validIds = new Set(existingSales.map(s => s.sale_id));
        }

        // Filter out sales_items with invalid sale_id references
        return data.filter(item => {
          if (!item.sale_id || item.sale_id === 'INVALID_SALE_ID') {
            console.warn(`Skipping sales_item with invalid sale_id: ${item.sale_id}`);
            return false;
          }
          if (!validIds.has(item.sale_id)) {
            console.warn(`Skipping sales_item with non-existent sale_id: ${item.sale_id}`);
            return false;
          }
          return true;
        });

      } else if (tableName === 'draft_sales_items') {
        // Get all existing draft_sale_ids from Supabase
        const { data: existingDraftSales, error } = await this.supabase
          .from('draft_sales')
          .select('draft_sale_id');

        if (!error && existingDraftSales) {
          validIds = new Set(existingDraftSales.map(ds => ds.draft_sale_id));
        }

        // Filter out draft_sales_items with invalid draft_sale_id references
        return data.filter(item => {
          if (!item.draft_sale_id || item.draft_sale_id === 'INVALID_DRAFT_SALE_ID') {
            console.warn(`Skipping draft_sales_item with invalid draft_sale_id: ${item.draft_sale_id}`);
            return false;
          }
          if (!validIds.has(item.draft_sale_id)) {
            console.warn(`Skipping draft_sales_item with non-existent draft_sale_id: ${item.draft_sale_id}`);
            return false;
          }
          return true;
        });
      }

      return data;

    } catch (error) {
      console.warn(`Warning: Could not validate ${tableName} references:`, error.message);
      // If validation fails, return empty array to avoid foreign key errors
      return [];
    }
  }



  // Get sync status for UI
  getSyncStatus() {
    return {
      status: this.syncStatus,
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime,
      errors: this.syncErrors,
      autoSyncActive: this.syncInterval !== null
    };
  }

  // Test connection to Supabase
  async testConnection() {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      console.log('✅ Supabase connection test successful');
      return { success: true, message: 'Connection successful' };
    } catch (error) {
      console.error('❌ Supabase connection test failed:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = { SupabaseSync, supabase };
