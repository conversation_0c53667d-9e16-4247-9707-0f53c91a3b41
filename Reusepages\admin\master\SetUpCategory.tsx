import { Button } from "@/components/ui/button"

export default function SetUpCategory() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Set Up Category</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-green-600 mb-4">Add New Category</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Category Name</label>
              <input 
                type="text" 
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter category name"
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Category Code</label>
              <input 
                type="text" 
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter category code"
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Parent Category</label>
              <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Select Parent Category (Optional)</option>
                <option value="A">A. Pipes</option>
                <option value="B">B. Nailpolish Remo</option>
                <option value="C">C. Miscellaneous</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Description</label>
              <textarea 
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                rows={3}
                placeholder="Enter category description"
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Display Order</label>
              <input 
                type="number" 
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter display order"
                min="1"
              />
            </div>
            <div>
              <label className="block text-sm font-bold text-gray-700 mb-2">Status</label>
              <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <Button className="bg-green-600 hover:bg-green-700 text-white font-bold w-full">
              Add Category
            </Button>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-blue-600 mb-4">Category Hierarchy</h3>
          <div className="space-y-3">
            <div className="border border-gray-200 rounded-lg p-3">
              <div className="font-bold text-gray-800">A. Pipes</div>
              <div className="ml-4 mt-2 space-y-1">
                <div className="text-sm text-gray-600">• Water Pipes</div>
                <div className="text-sm text-gray-600">• Dry Pipes</div>
                <div className="text-sm text-gray-600">• Metal Pipes</div>
              </div>
            </div>
            <div className="border border-gray-200 rounded-lg p-3">
              <div className="font-bold text-gray-800">A. Deli</div>
              <div className="ml-4 mt-2 space-y-1">
                <div className="text-sm text-gray-600">• Sandwiches</div>
                <div className="text-sm text-gray-600">• Salads</div>
                <div className="text-sm text-gray-600">• Hot Items</div>
              </div>
            </div>
            <div className="border border-gray-200 rounded-lg p-3">
              <div className="font-bold text-gray-800">A. Pills</div>
              <div className="ml-4 mt-2 space-y-1">
                <div className="text-sm text-gray-600">• Vitamins</div>
                <div className="text-sm text-gray-600">• Pain Relief</div>
                <div className="text-sm text-gray-600">• Supplements</div>
              </div>
            </div>
            <div className="border border-gray-200 rounded-lg p-3">
              <div className="font-bold text-gray-800">B. Nailpolish Remo</div>
              <div className="ml-4 mt-2 space-y-1">
                <div className="text-sm text-gray-600">• Removers</div>
                <div className="text-sm text-gray-600">• Pads</div>
                <div className="text-sm text-gray-600">• Cuticle Care</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-gray-800">Category Management</h3>
          <div className="flex gap-2">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold">
              Import Categories
            </Button>
            <Button className="bg-green-600 hover:bg-green-700 text-white font-bold">
              Export Categories
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b-2 border-gray-200">
                <th className="text-gray-700 p-3 font-bold">Code</th>
                <th className="text-gray-700 p-3 font-bold">Category Name</th>
                <th className="text-gray-700 p-3 font-bold">Parent Category</th>
                <th className="text-gray-700 p-3 font-bold">Products Count</th>
                <th className="text-gray-700 p-3 font-bold">Status</th>
                <th className="text-gray-700 p-3 font-bold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">A001</td>
                <td className="p-3 text-gray-800">A. Pipes</td>
                <td className="p-3 text-gray-600">-</td>
                <td className="p-3 text-gray-600">5 products</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Active
                  </span>
                </td>
                <td className="p-3">
                  <div className="flex gap-2">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                      Edit
                    </Button>
                    <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">A002</td>
                <td className="p-3 text-gray-800">Water Pipes</td>
                <td className="p-3 text-gray-600">A. Pipes</td>
                <td className="p-3 text-gray-600">2 products</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Active
                  </span>
                </td>
                <td className="p-3">
                  <div className="flex gap-2">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                      Edit
                    </Button>
                    <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">A003</td>
                <td className="p-3 text-gray-800">A. Deli</td>
                <td className="p-3 text-gray-600">-</td>
                <td className="p-3 text-gray-600">5 products</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Active
                  </span>
                </td>
                <td className="p-3">
                  <div className="flex gap-2">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                      Edit
                    </Button>
                    <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">B001</td>
                <td className="p-3 text-gray-800">B. Nailpolish Remo</td>
                <td className="p-3 text-gray-600">-</td>
                <td className="p-3 text-gray-600">5 products</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Active
                  </span>
                </td>
                <td className="p-3">
                  <div className="flex gap-2">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                      Edit
                    </Button>
                    <Button className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1">
                      Delete
                    </Button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
