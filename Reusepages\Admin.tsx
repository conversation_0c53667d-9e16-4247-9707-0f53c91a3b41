import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useNavigate } from "react-router-dom"
import {
  LayoutDashboard,
  Database,
  FileText,
  CreditCard,
  Package,
  Users,
  ArrowLeft,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  MapPin,
  Tag,
  Truck,
  ShoppingCart
} from "lucide-react"

// Import page components
import Dashboard from "./admin/Dashboard"
import Master from "./admin/Master"
import Reports from "./admin/Reports"
import Transactions from "./admin/Transactions"
import Wholesale from "./admin/Wholesale"
import AdminManagement from "./admin/AdminManagement"

// Import Master sub-pages
import SetUpLocation from "./admin/master/SetUpLocation"
import SetUpCategory from "./admin/master/SetUpCategory"
import SetUpSupplier from "./admin/master/SetUpSupplier"
import SetUpProduct from "./admin/master/SetUpProduct"

interface SidebarItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  hasDropdown?: boolean
  subItems?: SubItem[]
}

interface SubItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
}

const masterSubItems: SubItem[] = [
  { id: "setup-location", label: "Set Up Location", icon: MapPin },
  { id: "setup-category", label: "Set Up Category", icon: Tag },
  { id: "setup-supplier", label: "Set Up Supplier", icon: Truck },
  { id: "setup-product", label: "Set Up Product", icon: ShoppingCart },
]

const sidebarItems: SidebarItem[] = [
  { id: "dashboard", label: "Dashboard", icon: LayoutDashboard },
  { id: "master", label: "Master", icon: Database, hasDropdown: true, subItems: masterSubItems },
  { id: "reports", label: "Reports", icon: FileText },
  { id: "transactions", label: "Transactions", icon: CreditCard },
  { id: "wholesale", label: "Wholesale", icon: Package },
  { id: "admin-management", label: "Admin Management", icon: Users },
  { id: "back-to-pos", label: "Back To POS", icon: ArrowLeft },
]

export default function AdminPanel() {
  const navigate = useNavigate()
  const [isCollapsed, setIsCollapsed] = useState(true)
  const [activeItem, setActiveItem] = useState("setup-location") // Default to first Master sub-item
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isMasterExpanded, setIsMasterExpanded] = useState(true) // Master dropdown expanded by default

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", { 
      year: "numeric", 
      month: "2-digit", 
      day: "2-digit" 
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", { hour12: false })
  }

  const handleSidebarItemClick = (itemId: string) => {
    if (itemId === "back-to-pos") {
      navigate("/pos")
    } else if (itemId === "master") {
      setIsMasterExpanded(!isMasterExpanded)
    } else {
      setActiveItem(itemId)
      // If clicking a master sub-item, ensure master is expanded
      if (masterSubItems.some(subItem => subItem.id === itemId)) {
        setIsMasterExpanded(true)
      }
    }
  }

  const renderMainContent = () => {
    switch (activeItem) {
      case "dashboard":
        return <Dashboard />
      case "master":
        return <Master />
      case "setup-location":
        return <SetUpLocation />
      case "setup-category":
        return <SetUpCategory />
      case "setup-supplier":
        return <SetUpSupplier />
      case "setup-product":
        return <SetUpProduct />
      case "reports":
        return <Reports />
      case "transactions":
        return <Transactions />
      case "wholesale":
        return <Wholesale />
      case "admin-management":
        return <AdminManagement />
      default:
        return <SetUpLocation /> // Default to first Master sub-item
    }
  }

  return (
    <div className="h-screen bg-gray-50 text-gray-900 flex overflow-hidden">
      {/* Sidebar */}
      <div className={`bg-white border-r-2 border-gray-200 shadow-lg transition-all duration-300 flex-shrink-0 ${
        isCollapsed ? "w-16" : "w-64"
      }`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200 h-20 flex items-center">
          <div className="flex items-center justify-between w-full">
            {!isCollapsed && (
              <h2 className="text-lg font-black text-gray-800">ADMIN PANEL</h2>
            )}
            <Button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="bg-white text-green-600 border border-green-400 p-2 hover:bg-green-50 shadow-sm"
            >
              {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Sidebar Items */}
        <nav className="mt-4">
          {sidebarItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.id || (item.hasDropdown && masterSubItems.some(subItem => subItem.id === activeItem))

            return (
              <div key={item.id}>
                <button
                  onClick={() => handleSidebarItemClick(item.id)}
                  className={`w-full flex items-center gap-3 p-3 mx-2 rounded-lg transition-all duration-200 ${
                    isActive && !item.hasDropdown
                      ? "bg-green-500 text-white shadow-md"
                      : "text-gray-700 hover:bg-green-50 hover:text-green-600"
                  } ${item.id === "back-to-pos" ? "text-red-600 hover:text-red-700 hover:bg-red-50" : ""}`}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!isCollapsed && (
                    <>
                      <span className="font-bold text-sm flex-1 text-left">{item.label}</span>
                      {item.hasDropdown && (
                        <div className="ml-auto">
                          {isMasterExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </div>
                      )}
                    </>
                  )}
                </button>

                {/* Dropdown Sub-items */}
                {item.hasDropdown && isMasterExpanded && !isCollapsed && (
                  <div className="ml-4 mt-1 space-y-1">
                    {item.subItems?.map((subItem) => {
                      const SubIcon = subItem.icon
                      return (
                        <button
                          key={subItem.id}
                          onClick={() => handleSidebarItemClick(subItem.id)}
                          className={`w-full flex items-center gap-3 p-2 mx-2 rounded-lg transition-all duration-200 text-sm ${
                            activeItem === subItem.id
                              ? "bg-green-500 text-white shadow-md"
                              : "text-gray-600 hover:bg-green-50 hover:text-green-600"
                          }`}
                        >
                          <SubIcon className="h-4 w-4 flex-shrink-0" />
                          <span className="font-medium">{subItem.label}</span>
                        </button>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </nav>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Dashboard Header */}
        <div className="bg-white border-b-2 border-gray-200 p-4 flex-shrink-0 shadow-sm h-20">
          <div className="flex justify-between items-center h-full">
            <div>
              <h1 className="text-2xl font-black text-gray-800">ADMIN DASHBOARD</h1>
              <p className="text-lg font-bold text-green-600">Rainbow Station Inc.</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm font-bold text-gray-700">
                  Date: <span className="bg-gray-100 px-3 py-1 text-lg font-black text-gray-800 rounded">{formatDate(currentTime)}</span>
                </div>
                <div className="text-sm font-bold text-gray-700">
                  Time: <span className="bg-gray-100 px-3 py-1 text-lg font-black text-gray-800 rounded">{formatTime(currentTime)}</span>
                </div>
                <div className="text-sm font-bold text-gray-700">
                  Admin: <span className="bg-green-100 px-3 py-1 text-lg font-black text-green-800 rounded">Simon</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto bg-gray-50">
          {renderMainContent()}
        </div>
      </div>
    </div>
  )
}
