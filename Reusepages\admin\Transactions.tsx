import { Button } from "@/components/ui/button"

export default function Transactions() {
  return (
    <div className="p-6">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Transaction Management</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white border-2 border-green-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-green-600 mb-2">Today's Transactions</h3>
          <p className="text-3xl font-black text-gray-800">127</p>
          <p className="text-sm text-gray-600">+12% from yesterday</p>
        </div>
        <div className="bg-white border-2 border-blue-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-blue-600 mb-2">Total Amount</h3>
          <p className="text-3xl font-black text-gray-800">$2,450.75</p>
          <p className="text-sm text-gray-600">+8% from yesterday</p>
        </div>
        <div className="bg-white border-2 border-yellow-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-yellow-600 mb-2">Average Sale</h3>
          <p className="text-3xl font-black text-gray-800">$19.30</p>
          <p className="text-sm text-gray-600">-2% from yesterday</p>
        </div>
        <div className="bg-white border-2 border-red-400 rounded-lg p-6 shadow-md text-center">
          <h3 className="text-lg font-bold text-red-600 mb-2">Refunds</h3>
          <p className="text-3xl font-black text-gray-800">3</p>
          <p className="text-sm text-gray-600">Same as yesterday</p>
        </div>
      </div>
      
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-gray-800">Recent Transactions</h3>
          <div className="flex gap-2">
            <Button className="bg-green-600 hover:bg-green-700 text-white font-bold">
              Export CSV
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold">
              Filter
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b-2 border-gray-200">
                <th className="text-gray-700 p-3 font-bold">Transaction ID</th>
                <th className="text-gray-700 p-3 font-bold">Date & Time</th>
                <th className="text-gray-700 p-3 font-bold">Customer</th>
                <th className="text-gray-700 p-3 font-bold">Items</th>
                <th className="text-gray-700 p-3 font-bold">Amount</th>
                <th className="text-gray-700 p-3 font-bold">Payment Method</th>
                <th className="text-gray-700 p-3 font-bold">Status</th>
                <th className="text-gray-700 p-3 font-bold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">#TXN-001</td>
                <td className="p-3 text-gray-800">2024-01-15 14:30</td>
                <td className="p-3 text-gray-800">John Smith</td>
                <td className="p-3 text-gray-600">3 items</td>
                <td className="p-3 text-green-600 font-bold">$45.99</td>
                <td className="p-3 text-gray-600">Credit Card</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Completed
                  </span>
                </td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    View Details
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">#TXN-002</td>
                <td className="p-3 text-gray-800">2024-01-15 14:25</td>
                <td className="p-3 text-gray-800">Sarah Johnson</td>
                <td className="p-3 text-gray-600">1 item</td>
                <td className="p-3 text-green-600 font-bold">$23.50</td>
                <td className="p-3 text-gray-600">Cash</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Completed
                  </span>
                </td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    View Details
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">#TXN-003</td>
                <td className="p-3 text-gray-800">2024-01-15 14:20</td>
                <td className="p-3 text-gray-800">Mike Davis</td>
                <td className="p-3 text-gray-600">5 items</td>
                <td className="p-3 text-green-600 font-bold">$78.25</td>
                <td className="p-3 text-gray-600">Debit Card</td>
                <td className="p-3">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                    Completed
                  </span>
                </td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    View Details
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">#TXN-004</td>
                <td className="p-3 text-gray-800">2024-01-15 14:15</td>
                <td className="p-3 text-gray-800">Lisa Wilson</td>
                <td className="p-3 text-gray-600">2 items</td>
                <td className="p-3 text-red-600 font-bold">-$15.99</td>
                <td className="p-3 text-gray-600">Credit Card</td>
                <td className="p-3">
                  <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-bold">
                    Refunded
                  </span>
                </td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    View Details
                  </Button>
                </td>
              </tr>
              <tr className="border-b border-gray-100 hover:bg-gray-50">
                <td className="p-3 text-blue-600 font-medium">#TXN-005</td>
                <td className="p-3 text-gray-800">2024-01-15 14:10</td>
                <td className="p-3 text-gray-800">Robert Brown</td>
                <td className="p-3 text-gray-600">4 items</td>
                <td className="p-3 text-yellow-600 font-bold">$32.75</td>
                <td className="p-3 text-gray-600">Cash</td>
                <td className="p-3">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-bold">
                    Pending
                  </span>
                </td>
                <td className="p-3">
                  <Button className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-2 py-1">
                    View Details
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            Showing 5 of 127 transactions
          </div>
          <div className="flex gap-2">
            <Button className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 text-sm">
              Previous
            </Button>
            <Button className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 text-sm">
              Next
            </Button>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Payment Methods</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Credit Card</span>
              <span className="text-green-600 font-bold">65%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-600 h-2 rounded-full" style={{width: '65%'}}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Cash</span>
              <span className="text-blue-600 font-bold">25%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{width: '25%'}}></div>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Debit Card</span>
              <span className="text-yellow-600 font-bold">10%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-yellow-600 h-2 rounded-full" style={{width: '10%'}}></div>
            </div>
          </div>
        </div>
        
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Transaction Actions</h3>
          <div className="space-y-3">
            <Button className="bg-green-600 hover:bg-green-700 text-white font-bold w-full">
              Process Refund
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold w-full">
              Void Transaction
            </Button>
            <Button className="bg-yellow-600 hover:bg-yellow-700 text-white font-bold w-full">
              Print Receipt
            </Button>
            <Button className="bg-purple-600 hover:bg-purple-700 text-white font-bold w-full">
              Send Email Receipt
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
