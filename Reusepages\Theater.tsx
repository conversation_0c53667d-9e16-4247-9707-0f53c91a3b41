import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useNavigate } from "react-router-dom"
import { ArrowLeft, Users, TicketIcon, Ban, X } from "lucide-react"





export default function TheaterPage() {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState("tickets")

  const handleExit = () => {
    navigate("/pos")
  }

  const tabs = [
    { id: "tickets", label: "ISSUE TICKETS", icon: TicketIcon },
    { id: "current", label: "CURRENT VIEWER LIST", icon: Users },
    { id: "banned", label: "BANNED VIEWER LIST", icon: Ban },
    { id: "exit", label: "EXIT", icon: X },
  ]

  return (
    <div className="min-h-screen bg-white">

      {/* Header - Fixed and Modern */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-900 via-indigo-900 to-blue-900 text-white shadow-2xl">
        <div className="w-full mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <Button
                onClick={handleExit}
                variant="outline"
                size="sm"
                className="text-white border-white/30 bg-white/10 hover:bg-white/20 hover:text-white transition-all duration-200 font-semibold"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to POS
              </Button>
              <div>
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-black text-white tracking-wide">
                  THEATER MANAGEMENT SYSTEM
                </h1>
                <p className="text-sm sm:text-base text-blue-200 font-medium">Rainbow Station Inc.</p>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg border border-white/20">
                <p className="text-xs sm:text-sm text-blue-200 font-medium">Today</p>
                <p className="text-sm sm:text-base text-white font-bold">{new Date().toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation - Sticky below fixed header, full width */}
      <div className="fixed top-[88px] sm:top-[80px] left-0 right-0 z-40 bg-white border-b border-gray-200 shadow-md pt-4">
        <div className="w-full mx-auto">
          <nav className="flex w-full">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => {
                    if (tab.id === "exit") {
                      handleExit()
                    } else {
                      setActiveTab(tab.id)
                    }
                  }}
                  className={`flex items-center justify-center gap-2 py-4 text-sm sm:text-base font-bold whitespace-nowrap border-b-3 transition-all duration-200 flex-1 ${
                    activeTab === tab.id
                      ? "border-blue-600 text-blue-700 bg-blue-50 tab-shadow"
                      : "border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <Icon className="h-5 w-5 sm:h-6 sm:w-6 flex-shrink-0" />
                  <span className="font-bold">{tab.label}</span>
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Content - Account for fixed headers */}
      <div className="pt-[168px] sm:pt-[152px] min-h-screen bg-gray-50">
        <div className="w-full mx-auto p-4 md:p-6">
          {/* Issue Tickets Tab */}
          {activeTab === "tickets" && (
            <div className="flex items-center justify-center min-h-[60vh]">
              <Card className="border-blue-200 shadow-lg max-w-md w-full">
                <CardContent className="text-center py-16">
                  <TicketIcon className="h-24 w-24 text-blue-300 mx-auto mb-6" />
                  <h2 className="text-4xl font-black text-blue-600 mb-4">COMING SOON</h2>
                  <p className="text-lg text-gray-600 mb-2">Issue Tickets Feature</p>
                  <p className="text-sm text-gray-500">This feature is under development and will be available soon.</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Current Viewer List Tab */}
          {activeTab === "current" && (
            <div className="flex items-center justify-center min-h-[60vh]">
              <Card className="border-green-200 shadow-lg max-w-md w-full">
                <CardContent className="text-center py-16">
                  <Users className="h-24 w-24 text-green-300 mx-auto mb-6" />
                  <h2 className="text-4xl font-black text-green-600 mb-4">COMING SOON</h2>
                  <p className="text-lg text-gray-600 mb-2">Current Viewer List Feature</p>
                  <p className="text-sm text-gray-500">This feature is under development and will be available soon.</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Banned Viewer List Tab */}
          {activeTab === "banned" && (
            <div className="flex items-center justify-center min-h-[60vh]">
              <Card className="border-red-200 shadow-lg max-w-md w-full">
                <CardContent className="text-center py-16">
                  <Ban className="h-24 w-24 text-red-300 mx-auto mb-6" />
                  <h2 className="text-4xl font-black text-red-600 mb-4">COMING SOON</h2>
                  <p className="text-lg text-gray-600 mb-2">Banned Viewer List Feature</p>
                  <p className="text-sm text-gray-500">This feature is under development and will be available soon.</p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
