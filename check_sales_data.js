const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Open database
const dbPath = path.join(__dirname, 'pos_database.db');
const db = new sqlite3.Database(dbPath);

console.log('Checking database tables...\n');

// First check what tables exist
db.all(`
  SELECT name FROM sqlite_master WHERE type='table'
`, [], (err, tables) => {
  if (err) {
    console.error('Error:', err);
    db.close();
    return;
  }

  console.log('Available tables:');
  console.table(tables);

  // Check if sales table exists
  const salesTable = tables.find(t => t.name === 'sales');
  if (!salesTable) {
    console.log('\nSales table does not exist!');
    db.close();
    return;
  }

  // Check sales by operator
  db.all(`
    SELECT
      operator_name,
      DATE(sale_date) as sale_date,
      COUNT(*) as transaction_count,
      SUM(total_amount) as total_sales,
      location_name
    FROM sales
    WHERE sale_date >= date('now', '-7 days')
    GROUP BY operator_name, DATE(sale_date), location_name
    ORDER BY sale_date DESC, operator_name
    LIMIT 20
  `, [], (err, rows) => {
    if (err) {
      console.error('Error:', err);
    } else {
      console.log('\nSales by operator and date:');
      console.table(rows);
    }

    // Check total operators
    db.all(`
      SELECT DISTINCT operator_name, COUNT(*) as total_transactions
      FROM sales
      GROUP BY operator_name
      ORDER BY operator_name
    `, [], (err, operators) => {
      if (err) {
        console.error('Error:', err);
      } else {
        console.log('\nAll operators:');
        console.table(operators);
      }

      db.close();
    });
  });
});
