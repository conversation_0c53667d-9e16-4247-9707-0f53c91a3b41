<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Location - Rainbow Station POS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-label {
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
            margin-bottom: 10px;
        }
        
        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.exists {
            background: #4CAF50;
            color: white;
        }
        
        .status.missing {
            background: #f44336;
            color: white;
        }
        
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .button.secondary {
            background: #2196F3;
        }
        
        .button.secondary:hover {
            background: #1976D2;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .instructions h3 {
            color: #FFD700;
            margin-top: 0;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .loading {
            text-align: center;
            font-size: 18px;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Database Location Information</h1>
        
        <div id="loading" class="loading">
            Loading database information...
        </div>
        
        <div id="content" style="display: none;">
            <div class="info-section">
                <div class="info-label">📂 User Data Directory:</div>
                <div class="info-value" id="userDataPath"></div>
                <span id="userDataStatus" class="status"></span>
            </div>
            
            <div class="info-section">
                <div class="info-label">🗄️ Database File Path:</div>
                <div class="info-value" id="databasePath"></div>
                <span id="databaseStatus" class="status"></span>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <button class="button" onclick="openUserDataFolder()">📁 Open User Data Folder</button>
                <button class="button secondary" onclick="copyPath()">📋 Copy Database Path</button>
                <button class="button secondary" onclick="refreshInfo()">🔄 Refresh</button>
            </div>
            
            <div class="instructions">
                <h3>📋 How to Find Your Database File:</h3>
                <ol>
                    <li><strong>Method 1:</strong> Click the "Open User Data Folder" button above</li>
                    <li><strong>Method 2:</strong> Press <kbd>Windows + R</kbd>, paste the User Data Directory path, and press Enter</li>
                    <li><strong>Method 3:</strong> Open Windows Explorer and paste the path in the address bar</li>
                    <li><strong>Method 4:</strong> Navigate manually to: <code>C:\Users\<USER>\AppData\Roaming\rainbow-station-inc-pos\</code></li>
                </ol>
                
                <h3>📄 Database File Details:</h3>
                <ul>
                    <li><strong>File Name:</strong> pos_system.db</li>
                    <li><strong>Type:</strong> SQLite Database</li>
                    <li><strong>Contains:</strong> Users, Products, Sales, Tickets, etc.</li>
                    <li><strong>Backup:</strong> Copy this file to backup your data</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer, shell } = require('electron');
        
        let currentInfo = null;
        
        async function loadDatabaseInfo() {
            try {
                const info = await ipcRenderer.invoke('get-database-location');
                currentInfo = info;
                
                document.getElementById('userDataPath').textContent = info.userDataPath;
                document.getElementById('databasePath').textContent = info.databasePath;
                
                // Update status indicators
                const userDataStatus = document.getElementById('userDataStatus');
                const databaseStatus = document.getElementById('databaseStatus');
                
                if (info.userDataExists) {
                    userDataStatus.textContent = '✅ EXISTS';
                    userDataStatus.className = 'status exists';
                } else {
                    userDataStatus.textContent = '❌ MISSING';
                    userDataStatus.className = 'status missing';
                }
                
                if (info.databaseExists) {
                    databaseStatus.textContent = '✅ EXISTS';
                    databaseStatus.className = 'status exists';
                } else {
                    databaseStatus.textContent = '❌ MISSING';
                    databaseStatus.className = 'status missing';
                }
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                
            } catch (error) {
                console.error('Error loading database info:', error);
                document.getElementById('loading').textContent = 'Error loading database information';
            }
        }
        
        function openUserDataFolder() {
            if (currentInfo && currentInfo.userDataPath) {
                shell.openPath(currentInfo.userDataPath);
            }
        }
        
        function copyPath() {
            if (currentInfo && currentInfo.databasePath) {
                navigator.clipboard.writeText(currentInfo.databasePath).then(() => {
                    alert('Database path copied to clipboard!');
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = currentInfo.databasePath;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('Database path copied to clipboard!');
                });
            }
        }
        
        function refreshInfo() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';
            loadDatabaseInfo();
        }
        
        // Load info when page loads
        document.addEventListener('DOMContentLoaded', loadDatabaseInfo);
    </script>
</body>
</html>
