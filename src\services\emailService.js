const { Resend } = require('resend');
const fs = require('fs');
const path = require('path');

class EmailService {
    constructor() {
        this.resend = null;
        this.isEnabled = false;
        this.config = {
            apiKey: process.env.RESEND_API_KEY,
            businessOwnerEmail: process.env.BUSINESS_OWNER_EMAIL || '<EMAIL>',
            businessOwnerName: process.env.BUSINESS_OWNER_NAME || 'Rainbow Station Inc Owner',
            // Use Resend's default domain for testing if custom domain not set
            fromAddress: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
            fromName: process.env.EMAIL_FROM_NAME || 'Rainbow Station POS System',
            enabled: process.env.EMAIL_ENABLED !== 'false' // Default to true unless explicitly disabled
        };
        
        this.initialize();
    }

    initialize() {
        try {
            console.log('📧 Email Service: Starting initialization...');
            console.log('📧 Config check:');
            console.log('  - API Key:', this.config.apiKey ? `${this.config.apiKey.substring(0, 10)}...` : 'Not set');
            console.log('  - Business Email:', this.config.businessOwnerEmail);
            console.log('  - From Address:', this.config.fromAddress);
            console.log('  - Enabled:', this.config.enabled);

            if (!this.config.apiKey || this.config.apiKey === 're_xxxxxxxxx_your_actual_api_key_here') {
                console.warn('⚠️ Email Service: Resend API key not configured. Email functionality disabled.');
                console.warn('⚠️ Please set RESEND_API_KEY in .env.local file');
                return;
            }

            // Validate API key format
            if (!this.config.apiKey.startsWith('re_')) {
                console.warn('⚠️ Email Service: Invalid Resend API key format. Should start with "re_"');
                return;
            }

            if (!this.config.enabled) {
                console.log('📧 Email Service: Disabled via configuration (EMAIL_ENABLED=false)');
                return;
            }

            // Validate business owner email
            if (!this.config.businessOwnerEmail || !this.isValidEmail(this.config.businessOwnerEmail)) {
                console.warn('⚠️ Email Service: Invalid business owner email address');
                return;
            }

            this.resend = new Resend(this.config.apiKey);
            this.isEnabled = true;
            console.log('✅ Email Service: Initialized successfully');
            console.log('✅ Ready to send emails to:', this.config.businessOwnerEmail);
        } catch (error) {
            console.error('❌ Email Service: Initialization failed:', error);
            this.isEnabled = false;
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async sendDailySalesReport(reportData, csvContent, filters) {
        if (!this.isEnabled) {
            console.warn('📧 Email Service: Cannot send daily sales report - service disabled');
            return { success: false, message: 'Email service not available' };
        }

        try {
            const fileName = `daily-sales-report-${filters.dateFrom}-to-${filters.dateTo}.csv`;
            const subject = `Daily Sales Report - ${filters.dateFrom} to ${filters.dateTo}`;
            
            // Calculate totals for email summary
            const totals = this.calculateDailyTotals(reportData);
            
            const htmlContent = this.generateDailySalesEmailHTML(filters, totals);
            
            const emailData = {
                from: `${this.config.fromName} <${this.config.fromAddress}>`,
                to: [this.config.businessOwnerEmail],
                subject: subject,
                html: htmlContent,
                attachments: [
                    {
                        filename: fileName,
                        content: Buffer.from(csvContent).toString('base64'),
                        content_type: 'text/csv'
                    }
                ]
            };

            console.log('📧 Sending daily sales report email...');
            const result = await this.resend.emails.send(emailData);

            console.log('📧 Resend API response:', result);

            // Check if there's an error in the response
            if (result.error) {
                console.error('❌ Resend API error:', result.error);
                return {
                    success: false,
                    message: `Resend API error: ${result.error.message || result.error}`
                };
            }

            // The response should have an 'id' property directly (not under 'data')
            const messageId = result.id || result.data?.id;

            console.log('✅ Daily sales report email sent successfully:', messageId);
            return {
                success: true,
                messageId: messageId,
                message: 'Daily sales report sent successfully'
            };

        } catch (error) {
            console.error('❌ Failed to send daily sales report email:', error);
            console.error('❌ Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack,
                response: error.response?.data
            });
            return {
                success: false,
                message: `Failed to send email: ${error.message}`
            };
        }
    }

    async sendShiftSalesReport(shiftData, csvContent) {
        if (!this.isEnabled) {
            console.warn('📧 Email Service: Cannot send shift sales report - service disabled');
            return { success: false, message: 'Email service not available' };
        }

        try {
            const fileName = `shift-sales-report-${shiftData.shift.shift_id}.csv`;
            const subject = `Shift Sales Report - ${shiftData.shift.shift_id}`;
            
            // Calculate totals for email summary
            const totals = this.calculateShiftTotals(shiftData);
            
            const htmlContent = this.generateShiftSalesEmailHTML(shiftData, totals);
            
            const emailData = {
                from: `${this.config.fromName} <${this.config.fromAddress}>`,
                to: [this.config.businessOwnerEmail],
                subject: subject,
                html: htmlContent,
                attachments: [
                    {
                        filename: fileName,
                        content: Buffer.from(csvContent).toString('base64'),
                        content_type: 'text/csv'
                    }
                ]
            };

            console.log('📧 Sending shift sales report email...');
            const result = await this.resend.emails.send(emailData);

            console.log('📧 Resend API response:', result);

            // Check if there's an error in the response
            if (result.error) {
                console.error('❌ Resend API error:', result.error);
                return {
                    success: false,
                    message: `Resend API error: ${result.error.message || result.error}`
                };
            }

            // The response should have an 'id' property directly (not under 'data')
            const messageId = result.id || result.data?.id;

            console.log('✅ Shift sales report email sent successfully:', messageId);
            return {
                success: true,
                messageId: messageId,
                message: 'Shift sales report sent successfully'
            };

        } catch (error) {
            console.error('❌ Failed to send shift sales report email:', error);
            console.error('❌ Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack,
                response: error.response?.data
            });
            return {
                success: false,
                message: `Failed to send email: ${error.message}`
            };
        }
    }

    calculateDailyTotals(reportData) {
        const totals = {
            totalAmount: 0,
            totalCash: 0,
            totalDebit: 0,
            totalCredit: 0,
            saleTypes: {
                sale: { amount: 0, count: 0 },
                theater: { amount: 0, count: 0 },
                deli: { amount: 0, count: 0 }
            }
        };

        reportData.forEach(record => {
            const amount = parseFloat(record.total_amount) || 0;
            totals.totalAmount += amount;
            totals.totalCash += parseFloat(record.total_cash) || 0;
            totals.totalDebit += parseFloat(record.total_debit) || 0;
            totals.totalCredit += parseFloat(record.total_credit) || 0;
            
            if (totals.saleTypes[record.sale_type]) {
                totals.saleTypes[record.sale_type].amount += amount;
                totals.saleTypes[record.sale_type].count += 1;
            }
        });

        return totals;
    }

    calculateShiftTotals(shiftData) {
        const allTransactions = [...(shiftData.sales || []), ...(shiftData.tickets || [])];
        const totals = {
            totalAmount: 0,
            totalTransactions: allTransactions.length,
            saleTypes: {
                sale: { amount: 0, count: 0 },
                theater: { amount: 0, count: 0 },
                deli: { amount: 0, count: 0 }
            }
        };

        allTransactions.forEach(transaction => {
            const amount = parseFloat(transaction.total_amount) || 0;
            const isTicket = transaction.record_type === 'ticket' || transaction.ticket_id;
            const type = isTicket ? 'theater' : (transaction.sale_type || 'sale');
            
            totals.totalAmount += amount;
            totals.saleTypes[type].amount += amount;
            totals.saleTypes[type].count += 1;
        });

        return totals;
    }

    generateDailySalesEmailHTML(filters, totals) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Daily Sales Report</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: linear-gradient(135deg, #1e3a8a, #3730a3); color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .summary-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 16px 0; }
                .amount { font-size: 24px; font-weight: bold; color: #10b981; }
                .sale-type { display: inline-block; margin: 8px; padding: 8px 16px; border-radius: 6px; color: white; }
                .sale { background: #10b981; }
                .theater { background: #3b82f6; }
                .deli { background: #f59e0b; }
                .footer { background: #f1f5f9; padding: 16px; text-align: center; font-size: 12px; color: #64748b; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌈 Rainbow Station Inc</h1>
                <h2>Daily Sales Report</h2>
                <p>${filters.dateFrom} to ${filters.dateTo}</p>
            </div>
            
            <div class="content">
                <div class="summary-card">
                    <h3>📊 Sales Summary</h3>
                    <p><strong>Total Sales:</strong> <span class="amount">$${totals.totalAmount.toFixed(2)}</span></p>
                    <p><strong>Cash:</strong> $${totals.totalCash.toFixed(2)}</p>
                    <p><strong>Debit:</strong> $${totals.totalDebit.toFixed(2)}</p>
                    <p><strong>Credit:</strong> $${totals.totalCredit.toFixed(2)}</p>
                </div>
                
                <div class="summary-card">
                    <h3>🏪 Sales by Type</h3>
                    <div class="sale-type sale">
                        Regular Sales: $${totals.saleTypes.sale.amount.toFixed(2)} (${totals.saleTypes.sale.count} transactions)
                    </div>
                    <div class="sale-type theater">
                        Theater: $${totals.saleTypes.theater.amount.toFixed(2)} (${totals.saleTypes.theater.count} tickets)
                    </div>
                    <div class="sale-type deli">
                        Deli: $${totals.saleTypes.deli.amount.toFixed(2)} (${totals.saleTypes.deli.count} transactions)
                    </div>
                </div>
                
                <div class="summary-card">
                    <h3>📋 Report Details</h3>
                    <p><strong>Date Range:</strong> ${filters.dateFrom} to ${filters.dateTo}</p>
                    <p><strong>Shift Filter:</strong> ${filters.shift || 'All Shifts'}</p>
                    <p><strong>Sale Type Filter:</strong> ${filters.saleType || 'All Types'}</p>
                    <p><strong>Operator Filter:</strong> ${filters.operator || 'All Operators'}</p>
                    <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                </div>
                
                <p><strong>📎 Attachment:</strong> Detailed CSV report is attached to this email.</p>
            </div>
            
            <div class="footer">
                <p>This report was automatically generated by Rainbow Station Inc POS System</p>
                <p>For questions or support, please contact your system administrator</p>
            </div>
        </body>
        </html>
        `;
    }

    generateShiftSalesEmailHTML(shiftData, totals) {
        const shift = shiftData.shift;
        const startTime = new Date(shift.shift_start_time).toLocaleString();
        const endTime = shift.shift_end_time ? new Date(shift.shift_end_time).toLocaleString() : 'In Progress';
        
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Shift Sales Report</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: linear-gradient(135deg, #1e3a8a, #3730a3); color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .summary-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin: 16px 0; }
                .amount { font-size: 24px; font-weight: bold; color: #10b981; }
                .sale-type { display: inline-block; margin: 8px; padding: 8px 16px; border-radius: 6px; color: white; }
                .sale { background: #10b981; }
                .theater { background: #3b82f6; }
                .deli { background: #f59e0b; }
                .footer { background: #f1f5f9; padding: 16px; text-align: center; font-size: 12px; color: #64748b; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌈 Rainbow Station Inc</h1>
                <h2>Shift Sales Report</h2>
                <p>Shift ID: ${shift.shift_id}</p>
            </div>
            
            <div class="content">
                <div class="summary-card">
                    <h3>👤 Shift Information</h3>
                    <p><strong>Operator:</strong> ${shift.operator_name}</p>
                    <p><strong>Location:</strong> ${shift.location_name}</p>
                    <p><strong>Start Time:</strong> ${startTime}</p>
                    <p><strong>End Time:</strong> ${endTime}</p>
                    <p><strong>Status:</strong> ${shift.status}</p>
                </div>
                
                <div class="summary-card">
                    <h3>📊 Sales Summary</h3>
                    <p><strong>Total Sales:</strong> <span class="amount">$${totals.totalAmount.toFixed(2)}</span></p>
                    <p><strong>Total Transactions:</strong> ${totals.totalTransactions}</p>
                </div>
                
                <div class="summary-card">
                    <h3>🏪 Sales by Type</h3>
                    <div class="sale-type sale">
                        Regular Sales: $${totals.saleTypes.sale.amount.toFixed(2)} (${totals.saleTypes.sale.count} transactions)
                    </div>
                    <div class="sale-type theater">
                        Theater: $${totals.saleTypes.theater.amount.toFixed(2)} (${totals.saleTypes.theater.count} tickets)
                    </div>
                    <div class="sale-type deli">
                        Deli: $${totals.saleTypes.deli.amount.toFixed(2)} (${totals.saleTypes.deli.count} transactions)
                    </div>
                </div>
                
                <p><strong>📎 Attachment:</strong> Detailed CSV report is attached to this email.</p>
            </div>
            
            <div class="footer">
                <p>This report was automatically generated by Rainbow Station Inc POS System</p>
                <p>For questions or support, please contact your system administrator</p>
            </div>
        </body>
        </html>
        `;
    }

    async testConnection() {
        if (!this.isEnabled) {
            return { success: false, message: 'Email service not enabled' };
        }

        try {
            // Send a test email
            const result = await this.resend.emails.send({
                from: `${this.config.fromName} <${this.config.fromAddress}>`,
                to: [this.config.businessOwnerEmail],
                subject: 'Rainbow Station POS - Email Service Test',
                html: `
                <h2>🌈 Email Service Test</h2>
                <p>This is a test email to verify that the Resend email service is working correctly.</p>
                <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                <p>If you receive this email, the email service is configured properly.</p>
                `
            });

            console.log('📧 Test email response:', result);

            // Check if there's an error in the response
            if (result.error) {
                console.error('❌ Test email error:', result.error);
                return {
                    success: false,
                    message: `Test email failed: ${result.error.message || result.error}`
                };
            }

            // The response should have an 'id' property directly (not under 'data')
            const messageId = result.id || result.data?.id;

            return {
                success: true,
                messageId: messageId,
                message: 'Test email sent successfully'
            };
        } catch (error) {
            return { 
                success: false, 
                message: `Test email failed: ${error.message}`
            };
        }
    }
}

module.exports = EmailService;
