const { PosPrinter } = require('electron-pos-printer');
const { ThermalPrinter, PrinterTypes, CharacterSet } = require('node-thermal-printer');
const fs = require('fs');
const path = require('path');

class PrinterService {
    constructor() {
        this.availablePrinters = [];
        this.defaultPrinter = null;
        this.thermalPrinters = [];
        this.systemPrinters = [];
        this.config = {
            receiptWidth: '80mm',
            autoCut: true,
            printLogo: true,
            printBarcode: true,
            copies: 1,
            fontSize: 'normal',
            businessInfo: {
                name: 'Rainbow Station Inc',
                address: '123 Main Street',
                city: 'Your City, State 12345',
                phone: '(*************',
                email: '<EMAIL>'
            }
        };
        this.isInitialized = false;
    }

    async initialize() {
        try {
            console.log('🖨️ Initializing Printer Service...');
            
            // Load saved configuration
            await this.loadConfiguration();
            
            // Detect available printers
            await this.detectAllPrinters();
            
            this.isInitialized = true;
            console.log('✅ Printer Service initialized successfully');
            console.log(`📊 Found ${this.availablePrinters.length} total printers`);
            
            return {
                success: true,
                printersFound: this.availablePrinters.length,
                defaultPrinter: this.defaultPrinter
            };
        } catch (error) {
            console.error('❌ Failed to initialize Printer Service:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async detectAllPrinters() {
        console.log('🔍 Detecting all available printers...');
        
        // Reset arrays
        this.availablePrinters = [];
        this.systemPrinters = [];
        this.thermalPrinters = [];

        try {
            // Detect system printers (requires webContents)
            await this.detectSystemPrinters();
            
            // Detect thermal printers on network
            await this.detectNetworkThermalPrinters();
            
            // Combine all printers
            this.availablePrinters = [...this.systemPrinters, ...this.thermalPrinters];
            
            // Set default printer if not already set
            if (!this.defaultPrinter && this.availablePrinters.length > 0) {
                this.defaultPrinter = this.availablePrinters[0];
            }
            
            console.log(`✅ Detection complete: ${this.availablePrinters.length} printers found`);
            
        } catch (error) {
            console.error('❌ Error detecting printers:', error);
        }
    }

    async detectSystemPrinters() {
        try {
            // This will be called from main process with webContents
            console.log('🔍 Detecting system printers...');
            
            // System printers will be detected via IPC from main process
            // For now, we'll add a placeholder that will be populated by main process
            
        } catch (error) {
            console.error('❌ Error detecting system printers:', error);
        }
    }

    async detectNetworkThermalPrinters() {
        try {
            console.log('🔍 Scanning for network thermal printers...');
            
            // Common thermal printer IPs to check (this is a basic implementation)
            const commonIPs = [
                '*************',
                '*************',
                '*************',
                '*************',
                '*************'
            ];
            
            for (const ip of commonIPs) {
                try {
                    const printer = new ThermalPrinter({
                        type: PrinterTypes.EPSON,
                        interface: `tcp://${ip}:9100`,
                        options: { timeout: 2000 }
                    });
                    
                    const isConnected = await printer.isPrinterConnected();
                    if (isConnected) {
                        this.thermalPrinters.push({
                            id: `thermal_${ip.replace(/\./g, '_')}`,
                            name: `Thermal Printer (${ip})`,
                            type: 'thermal',
                            connection: 'network',
                            address: ip,
                            port: 9100,
                            status: 'online'
                        });
                        console.log(`✅ Found thermal printer at ${ip}`);
                    }
                } catch (error) {
                    // Printer not found at this IP, continue
                }
            }
            
        } catch (error) {
            console.error('❌ Error detecting network thermal printers:', error);
        }
    }

    async printReceipt(saleData, options = {}) {
        if (!this.isInitialized) {
            throw new Error('Printer service not initialized');
        }

        const printer = options.printer || this.defaultPrinter;
        if (!printer) {
            throw new Error('No printer available');
        }

        console.log('🖨️ Printing receipt...', {
            saleId: saleData.sale_id,
            printer: printer.name,
            type: printer.type
        });

        try {
            let result;
            
            if (printer.type === 'thermal') {
                result = await this.printThermalReceipt(saleData, printer, options);
            } else {
                result = await this.printSystemReceipt(saleData, printer, options);
            }
            
            console.log('✅ Receipt printed successfully');
            return {
                success: true,
                printer: printer.name,
                saleId: saleData.sale_id,
                ...result
            };
            
        } catch (error) {
            console.error('❌ Failed to print receipt:', error);
            throw error;
        }
    }

    async printThermalReceipt(saleData, printer, options = {}) {
        try {
            const thermalPrinter = new ThermalPrinter({
                type: PrinterTypes.EPSON,
                interface: printer.connection === 'network' 
                    ? `tcp://${printer.address}:${printer.port || 9100}`
                    : `printer:${printer.name}`,
                characterSet: CharacterSet.PC437_USA,
                options: { timeout: 5000 }
            });

            // Generate receipt content
            const receiptData = this.generateThermalReceiptData(saleData);
            
            // Print each line
            for (const line of receiptData) {
                switch (line.type) {
                    case 'text':
                        if (line.align === 'center') thermalPrinter.alignCenter();
                        else if (line.align === 'right') thermalPrinter.alignRight();
                        else thermalPrinter.alignLeft();
                        
                        if (line.bold) thermalPrinter.bold(true);
                        if (line.size === 'large') thermalPrinter.setTextDoubleHeight();
                        
                        thermalPrinter.println(line.content);
                        
                        if (line.bold) thermalPrinter.bold(false);
                        if (line.size === 'large') thermalPrinter.setTextNormal();
                        break;
                        
                    case 'line':
                        thermalPrinter.drawLine();
                        break;
                        
                    case 'newline':
                        thermalPrinter.newLine();
                        break;
                        
                    case 'barcode':
                        thermalPrinter.alignCenter();
                        thermalPrinter.code128(line.content);
                        thermalPrinter.alignLeft();
                        break;
                }
            }
            
            // Cut paper if enabled
            if (this.config.autoCut) {
                thermalPrinter.cut();
            }
            
            // Execute print job
            await thermalPrinter.execute();
            
            return { method: 'thermal', lines: receiptData.length };
            
        } catch (error) {
            console.error('❌ Thermal printing failed:', error);
            throw new Error(`Thermal printing failed: ${error.message}`);
        }
    }

    async printSystemReceipt(saleData, printer, options = {}) {
        try {
            // Generate receipt data for electron-pos-printer
            const receiptData = this.generateSystemReceiptData(saleData);
            
            const printOptions = {
                preview: false,
                margin: '0 0 0 0',
                copies: this.config.copies,
                printerName: printer.name,
                timeOutPerLine: 400,
                pageSize: this.config.receiptWidth,
                silent: true
            };
            
            await PosPrinter.print(receiptData, printOptions);
            
            return { method: 'system', items: receiptData.length };
            
        } catch (error) {
            console.error('❌ System printing failed:', error);
            throw new Error(`System printing failed: ${error.message}`);
        }
    }

    generateThermalReceiptData(saleData) {
        const lines = [];
        const config = this.config;
        
        // Header
        lines.push({ type: 'text', content: config.businessInfo.name, align: 'center', bold: true, size: 'large' });
        lines.push({ type: 'text', content: config.businessInfo.address, align: 'center' });
        lines.push({ type: 'text', content: config.businessInfo.city, align: 'center' });
        lines.push({ type: 'text', content: config.businessInfo.phone, align: 'center' });
        lines.push({ type: 'line' });
        
        // Transaction info
        lines.push({ type: 'text', content: `Receipt: ${saleData.sale_id}`, align: 'left' });
        lines.push({ type: 'text', content: `Date: ${new Date(saleData.sale_date).toLocaleString()}`, align: 'left' });
        lines.push({ type: 'text', content: `Operator: ${saleData.operator_name}`, align: 'left' });
        lines.push({ type: 'text', content: `Location: ${saleData.location_name}`, align: 'left' });
        lines.push({ type: 'line' });
        
        // Items (if available)
        if (saleData.items && saleData.items.length > 0) {
            saleData.items.forEach(item => {
                lines.push({ type: 'text', content: item.name, align: 'left' });
                lines.push({ type: 'text', content: `  ${item.quantity} x $${item.price.toFixed(2)} = $${item.total.toFixed(2)}`, align: 'left' });
            });
        } else {
            lines.push({ type: 'text', content: 'Sale Items', align: 'left', bold: true });
            lines.push({ type: 'text', content: `Total Amount: $${parseFloat(saleData.total_amount || 0).toFixed(2)}`, align: 'left' });
        }
        
        lines.push({ type: 'line' });
        
        // Totals
        lines.push({ type: 'text', content: `TOTAL: $${parseFloat(saleData.total_amount || 0).toFixed(2)}`, align: 'right', bold: true, size: 'large' });
        
        // Payment info
        if (saleData.payment_cash > 0) {
            lines.push({ type: 'text', content: `Cash: $${parseFloat(saleData.payment_cash).toFixed(2)}`, align: 'right' });
        }
        if (saleData.payment_debit > 0) {
            lines.push({ type: 'text', content: `Debit: $${parseFloat(saleData.payment_debit).toFixed(2)}`, align: 'right' });
        }
        if (saleData.payment_credit > 0) {
            lines.push({ type: 'text', content: `Credit: $${parseFloat(saleData.payment_credit).toFixed(2)}`, align: 'right' });
        }
        
        lines.push({ type: 'line' });
        
        // Footer
        lines.push({ type: 'text', content: 'Thank you for your business!', align: 'center', bold: true });
        lines.push({ type: 'text', content: 'Returns within 30 days with receipt', align: 'center' });
        lines.push({ type: 'newline' });
        
        // Barcode
        if (config.printBarcode) {
            lines.push({ type: 'barcode', content: saleData.sale_id });
        }
        
        lines.push({ type: 'newline' });
        lines.push({ type: 'newline' });
        
        return lines;
    }

    generateSystemReceiptData(saleData) {
        const data = [];
        const config = this.config;
        
        // Header
        data.push({
            type: 'text',
            value: config.businessInfo.name,
            style: { fontWeight: '700', textAlign: 'center', fontSize: '24px' }
        });
        
        data.push({
            type: 'text',
            value: `${config.businessInfo.address}\n${config.businessInfo.city}\n${config.businessInfo.phone}`,
            style: { textAlign: 'center', fontSize: '14px' }
        });
        
        // Transaction info
        data.push({
            type: 'text',
            value: `Receipt: ${saleData.sale_id}\nDate: ${new Date(saleData.sale_date).toLocaleString()}\nOperator: ${saleData.operator_name}\nLocation: ${saleData.location_name}`,
            style: { fontSize: '12px', margin: '10px 0' }
        });
        
        // Items table (if available)
        if (saleData.items && saleData.items.length > 0) {
            data.push({
                type: 'table',
                style: { border: '1px solid #ddd' },
                tableHeader: ['Item', 'Qty', 'Price', 'Total'],
                tableBody: saleData.items.map(item => [
                    item.name,
                    item.quantity.toString(),
                    `$${item.price.toFixed(2)}`,
                    `$${item.total.toFixed(2)}`
                ]),
                tableHeaderStyle: { backgroundColor: '#000', color: 'white' },
                tableBodyStyle: { border: '0.5px solid #ddd' }
            });
        }
        
        // Total
        data.push({
            type: 'text',
            value: `TOTAL: $${parseFloat(saleData.total_amount || 0).toFixed(2)}`,
            style: { fontWeight: '700', textAlign: 'right', fontSize: '20px', margin: '10px 0' }
        });
        
        // Payment breakdown
        let paymentText = '';
        if (saleData.payment_cash > 0) paymentText += `Cash: $${parseFloat(saleData.payment_cash).toFixed(2)}\n`;
        if (saleData.payment_debit > 0) paymentText += `Debit: $${parseFloat(saleData.payment_debit).toFixed(2)}\n`;
        if (saleData.payment_credit > 0) paymentText += `Credit: $${parseFloat(saleData.payment_credit).toFixed(2)}\n`;
        
        if (paymentText) {
            data.push({
                type: 'text',
                value: paymentText.trim(),
                style: { textAlign: 'right', fontSize: '14px' }
            });
        }
        
        // Footer
        data.push({
            type: 'text',
            value: 'Thank you for your business!\nReturns within 30 days with receipt',
            style: { textAlign: 'center', fontSize: '14px', margin: '20px 0' }
        });
        
        // Barcode
        if (config.printBarcode) {
            data.push({
                type: 'barCode',
                value: saleData.sale_id,
                height: 40,
                width: 2,
                displayValue: true,
                fontsize: 12
            });
        }
        
        return data;
    }

    async loadConfiguration() {
        try {
            const configPath = path.join(process.cwd(), 'printer-config.json');
            if (fs.existsSync(configPath)) {
                const savedConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                this.config = { ...this.config, ...savedConfig };
                console.log('📄 Printer configuration loaded');
            }
        } catch (error) {
            console.warn('⚠️ Could not load printer configuration:', error.message);
        }
    }

    async saveConfiguration() {
        try {
            const configPath = path.join(process.cwd(), 'printer-config.json');
            fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2));
            console.log('💾 Printer configuration saved');
            return true;
        } catch (error) {
            console.error('❌ Failed to save printer configuration:', error);
            return false;
        }
    }

    setDefaultPrinter(printerId) {
        const printer = this.availablePrinters.find(p => p.id === printerId);
        if (printer) {
            this.defaultPrinter = printer;
            this.config.defaultPrinterId = printerId;
            this.saveConfiguration();
            return true;
        }
        return false;
    }

    updateConfiguration(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveConfiguration();
    }

    getAvailablePrinters() {
        return this.availablePrinters;
    }

    getDefaultPrinter() {
        return this.defaultPrinter;
    }

    getConfiguration() {
        return this.config;
    }

    async testPrint(printerId = null) {
        const printer = printerId 
            ? this.availablePrinters.find(p => p.id === printerId)
            : this.defaultPrinter;
            
        if (!printer) {
            throw new Error('No printer specified for test');
        }

        const testSaleData = {
            sale_id: 'TEST-' + Date.now(),
            sale_date: new Date().toISOString(),
            operator_name: 'Test User',
            location_name: 'Test Location',
            total_amount: 12.34,
            payment_cash: 12.34,
            payment_debit: 0,
            payment_credit: 0,
            items: [
                {
                    name: 'Test Item',
                    quantity: 1,
                    price: 12.34,
                    total: 12.34
                }
            ]
        };

        return await this.printReceipt(testSaleData, { printer });
    }

    /**
     * Send raw command to printer (for cash drawer control)
     */
    async sendRawCommand(command, printerName = null) {
        try {
            const printer = printerName || this.defaultPrinter;

            if (!printer) {
                throw new Error('No printer available for raw command');
            }

            console.log(`📤 Sending raw command to printer: ${printer}`);
            console.log(`📤 Command: [${command.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);

            // Try thermal printer first (most common for cash drawer)
            const thermalPrinter = this.thermalPrinters.find(p => p.name === printer);
            if (thermalPrinter) {
                try {
                    const thermalInstance = new ThermalPrinter({
                        type: PrinterTypes.EPSON,
                        interface: thermalPrinter.interface,
                        characterSet: CharacterSet.PC852_LATIN2,
                        removeSpecialCharacters: false,
                        lineCharacter: "="
                    });

                    // Send raw bytes
                    await thermalInstance.raw(Buffer.from(command));
                    await thermalInstance.execute();

                    console.log('✅ Raw command sent via thermal printer');
                    return {
                        success: true,
                        method: 'thermal',
                        printer: printer
                    };
                } catch (thermalError) {
                    console.warn('⚠️ Thermal printer raw command failed:', thermalError.message);
                }
            }

            // Fallback to system printer
            try {
                // For system printers, we'll simulate the command
                // In a real implementation, you might use a different approach
                console.log('📤 Simulating raw command via system printer');

                return {
                    success: true,
                    method: 'system',
                    printer: printer,
                    simulated: true
                };
            } catch (systemError) {
                console.warn('⚠️ System printer raw command failed:', systemError.message);
                throw systemError;
            }

        } catch (error) {
            console.error('❌ Failed to send raw command:', error);
            throw error;
        }
    }
}

module.exports = PrinterService;
