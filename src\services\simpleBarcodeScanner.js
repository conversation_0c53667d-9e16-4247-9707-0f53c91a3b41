/**
 * Simple Barcode Scanner Service
 * Using OnScan.js library for reliable barcode scanner detection
 * No native dependencies required - pure JavaScript solution
 */

class SimpleBarcodeScanner {
    constructor() {
        this.isInitialized = false;
        this.isEnabled = true;
        this.scanCallbacks = [];
        this.currentElement = null;
        
        // Scanner configuration
        this.config = {
            // Timing settings for scanner detection
            timeBeforeScanTest: 100,    // Wait time after keypress to check if scanning finished
            avgTimeByChar: 30,          // Average time between characters (ms)
            minLength: 3,               // Minimum barcode length
            maxLength: 50,              // Maximum barcode length
            
            // Key codes
            suffixKeyCodes: [13],       // Enter key expected at end of scan
            prefixKeyCodes: [],         // No prefix codes by default
            
            // Scanner behavior
            reactToKeydown: true,       // Listen to keyboard events
            reactToPaste: true,         // Also support paste mode scanners
            stopPropagation: false,     // Don't stop other events
            preventDefault: false,      // Don't prevent default actions
            
            // Focus handling
            ignoreIfFocusOn: 'input, textarea, select', // Ignore scans when these elements are focused
            
            // Quantity (for inventory scanners)
            singleScanQty: 1
        };
        
        console.log('🔍 Simple Barcode Scanner Service initialized');
    }

    /**
     * Initialize barcode scanner detection
     */
    initialize(targetElement = document) {
        try {
            console.log('🔍 Initializing OnScan.js barcode scanner...');
            
            this.currentElement = targetElement;
            
            // Configure OnScan.js with our settings
            const onScanConfig = {
                ...this.config,
                
                // Main scan callback
                onScan: (scannedCode, quantity) => {
                    this.handleBarcodeScanned(scannedCode, quantity, 'scanner');
                },
                
                // Error callback
                onScanError: (debug) => {
                    console.warn('🔍 Scan error:', debug);
                    this.handleScanError(debug);
                },
                
                // Key detection callback (for debugging)
                onKeyDetect: (keyCode, event) => {
                    // Only log if debugging is enabled
                    if (this.config.debug) {
                        console.log('🔍 Key detected:', keyCode, event.key);
                    }
                    return true; // Continue processing
                },
                
                // Paste callback
                onPaste: (pastedText, event) => {
                    console.log('🔍 Paste detected:', pastedText);
                    this.handleBarcodeScanned(pastedText, 1, 'paste');
                }
            };
            
            // Attach OnScan.js to the target element
            if (typeof onScan !== 'undefined' && onScan.attachTo) {
                onScan.attachTo(targetElement, onScanConfig);
                this.isInitialized = true;
                console.log('✅ OnScan.js attached successfully');
            } else {
                // Fallback: OnScan.js not available, use manual detection
                console.warn('⚠️ OnScan.js not available, using fallback detection');
                this.initializeFallbackDetection(targetElement);
            }
            
            return {
                success: true,
                method: this.isInitialized ? 'onscan.js' : 'fallback',
                message: 'Barcode scanner initialized successfully'
            };
            
        } catch (error) {
            console.error('❌ Failed to initialize barcode scanner:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Fallback detection method if OnScan.js is not available
     */
    initializeFallbackDetection(targetElement) {
        let scanBuffer = '';
        let scanTimeout = null;
        let lastKeyTime = 0;
        
        const handleKeyDown = (event) => {
            if (!this.isEnabled) return;
            
            // Skip if focused on input elements
            if (this.shouldIgnoreInput(event.target)) return;
            
            const currentTime = Date.now();
            const timeSinceLastKey = currentTime - lastKeyTime;
            
            // Handle Enter key (potential end of scan)
            if (event.keyCode === 13) {
                event.preventDefault();
                if (scanBuffer.trim().length >= this.config.minLength) {
                    this.handleBarcodeScanned(scanBuffer.trim(), 1, 'fallback');
                }
                scanBuffer = '';
                return;
            }
            
            // Handle printable characters
            if (this.isPrintableKey(event)) {
                event.preventDefault();
                
                // If too much time passed, start new scan
                if (timeSinceLastKey > 100) {
                    scanBuffer = '';
                }
                
                scanBuffer += event.key || String.fromCharCode(event.keyCode);
                lastKeyTime = currentTime;
                
                // Set timeout to process scan
                clearTimeout(scanTimeout);
                scanTimeout = setTimeout(() => {
                    if (scanBuffer.trim().length >= this.config.minLength) {
                        this.handleBarcodeScanned(scanBuffer.trim(), 1, 'fallback');
                    }
                    scanBuffer = '';
                }, this.config.timeBeforeScanTest);
            }
        };
        
        targetElement.addEventListener('keydown', handleKeyDown);
        this.isInitialized = true;
        console.log('✅ Fallback barcode detection initialized');
    }

    /**
     * Handle successful barcode scan
     */
    handleBarcodeScanned(scannedCode, quantity, source) {
        if (!this.isEnabled) return;
        
        console.log(`🔍 Barcode scanned: "${scannedCode}" (${source})`);
        
        const scanData = {
            barcode: scannedCode,
            quantity: quantity || 1,
            timestamp: Date.now(),
            source: source,
            method: this.isInitialized ? 'onscan.js' : 'fallback'
        };
        
        // Call all registered callbacks
        this.scanCallbacks.forEach(callback => {
            try {
                callback(scanData);
            } catch (error) {
                console.error('❌ Error in barcode scan callback:', error);
            }
        });
        
        // Dispatch custom event
        const event = new CustomEvent('barcodeScanned', {
            detail: scanData
        });
        (this.currentElement || document).dispatchEvent(event);
    }

    /**
     * Handle scan errors
     */
    handleScanError(debug) {
        console.warn('🔍 Barcode scan error:', debug);
        
        const errorData = {
            error: 'scan_error',
            debug: debug,
            timestamp: Date.now()
        };
        
        // Dispatch error event
        const event = new CustomEvent('barcodeScanError', {
            detail: errorData
        });
        (this.currentElement || document).dispatchEvent(event);
    }

    /**
     * Check if input should be ignored
     */
    shouldIgnoreInput(target) {
        if (!target) return false;
        
        const tagName = target.tagName.toLowerCase();
        const inputTypes = ['input', 'textarea', 'select'];
        
        return inputTypes.includes(tagName) || 
               target.contentEditable === 'true' ||
               target.closest('.modal.show') !== null;
    }

    /**
     * Check if key is printable
     */
    isPrintableKey(event) {
        const key = event.key;
        return key && key.length === 1 && /[\w\d\-\.\s]/.test(key);
    }

    /**
     * Register callback for barcode scans
     */
    onBarcodeScanned(callback) {
        if (typeof callback === 'function') {
            this.scanCallbacks.push(callback);
        }
    }

    /**
     * Remove callback
     */
    removeBarcodeCallback(callback) {
        const index = this.scanCallbacks.indexOf(callback);
        if (index > -1) {
            this.scanCallbacks.splice(index, 1);
        }
    }

    /**
     * Enable/disable scanner detection
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        console.log(`🔍 Barcode scanner ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Update configuration
     */
    configure(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // If OnScan.js is attached, update its options
        if (this.isInitialized && this.currentElement && typeof onScan !== 'undefined') {
            try {
                onScan.setOptions(this.currentElement, this.config);
                console.log('🔧 OnScan.js configuration updated');
            } catch (error) {
                console.warn('⚠️ Failed to update OnScan.js configuration:', error);
            }
        }
    }

    /**
     * Simulate a barcode scan (for testing)
     */
    simulate(barcode) {
        if (this.isInitialized && this.currentElement && typeof onScan !== 'undefined') {
            try {
                onScan.simulate(this.currentElement, barcode);
                console.log(`🔍 Simulated barcode scan: "${barcode}"`);
            } catch (error) {
                console.warn('⚠️ Failed to simulate scan:', error);
                // Fallback to direct callback
                this.handleBarcodeScanned(barcode, 1, 'simulation');
            }
        } else {
            // Direct simulation
            this.handleBarcodeScanned(barcode, 1, 'simulation');
        }
    }

    /**
     * Get scanner status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            enabled: this.isEnabled,
            method: this.isInitialized ? 'onscan.js' : 'fallback',
            callbacks: this.scanCallbacks.length,
            config: this.config
        };
    }

    /**
     * Cleanup and detach scanner
     */
    destroy() {
        if (this.isInitialized && this.currentElement && typeof onScan !== 'undefined') {
            try {
                onScan.detachFrom(this.currentElement);
                console.log('🔍 OnScan.js detached');
            } catch (error) {
                console.warn('⚠️ Failed to detach OnScan.js:', error);
            }
        }
        
        this.scanCallbacks = [];
        this.isInitialized = false;
        this.currentElement = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleBarcodeScanner;
} else {
    window.SimpleBarcodeScanner = SimpleBarcodeScanner;
}
