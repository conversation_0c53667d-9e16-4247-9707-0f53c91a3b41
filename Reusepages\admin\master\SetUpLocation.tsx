import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"

interface LocationData {
  locationCode: string
  location: string
  companyName: string
  address1: string
  address2: string
  phone: string
  taxPercent: string
  email: string
  appMode: string
  theaterPLU: string
  theaterTime: string
  deli: boolean
}

interface LocationListItem {
  locationCode: string
  location: string
  companyName: string
  address1: string
  address2: string
  phone: string
  taxPercent: string
}

export default function SetUpLocation() {
  const [formData, setFormData] = useState<LocationData>({
    locationCode: "",
    location: "",
    companyName: "",
    address1: "",
    address2: "",
    phone: "",
    taxPercent: "",
    email: "",
    appMode: "",
    theaterPLU: "",
    theaterTime: "",
    deli: false
  })

  const [locations] = useState<LocationListItem[]>([
    {
      locationCode: "HU",
      location: "Huntington",
      companyName: "Love Toys",
      address1: "700 Jericho Turnpike",
      address2: "Huntington Station, NY 11746",
      phone: "1631 923 ...",
      taxPercent: "8.625"
    },
    {
      locationCode: "PC",
      location: "LOVE TOYS",
      companyName: "Pacific Av...",
      address1: "1626 Pacific Ave",
      address2: "Atlantic city, N...",
      phone: "**********",
      taxPercent: "6.625"
    },
    {
      locationCode: "MP",
      location: "Miami Play...",
      companyName: "",
      address1: "",
      address2: "",
      phone: "",
      taxPercent: "0.000"
    },
    {
      locationCode: "PP",
      location: "Pompano B...",
      companyName: "",
      address1: "",
      address2: "",
      phone: "",
      taxPercent: "0.000"
    },
    {
      locationCode: "RS",
      location: "Rainbow St...",
      companyName: "RAINBO...",
      address1: "203 8th Ave",
      address2: "New York,NY 1...",
      phone: "212-206-7...",
      taxPercent: "8.875"
    }
  ])

  const handleInputChange = (field: keyof LocationData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = () => {
    console.log("Saving location:", formData)
  }

  const handleClear = () => {
    setFormData({
      locationCode: "",
      location: "",
      companyName: "",
      address1: "",
      address2: "",
      phone: "",
      taxPercent: "",
      email: "",
      appMode: "",
      theaterPLU: "",
      theaterTime: "",
      deli: false
    })
  }

  const handleDelete = () => {
    console.log("Deleting location")
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-3xl font-black text-gray-800 mb-6">Set Up Location</h2>

      {/* Top Action Buttons */}
      <div className="flex gap-3 mb-6">
        <Button
          onClick={handleSave}
          className="bg-green-600 hover:bg-green-700 text-white font-bold px-8 py-2"
        >
          SAVE
        </Button>
        <Button
          onClick={handleClear}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold px-8 py-2"
        >
          CLEAR
        </Button>
        <Button
          onClick={handleDelete}
          className="bg-red-600 hover:bg-red-700 text-white font-bold px-8 py-2"
        >
          DELETE
        </Button>
      </div>

      {/* Main Form */}
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg">
        <div className="p-6 space-y-6">
          {/* Location Code and Location Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Location Code</Label>
              <Input
                value={formData.locationCode}
                onChange={(e) => handleInputChange("locationCode", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter location code"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Location</Label>
              <Input
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter location name"
              />
            </div>
          </div>

          {/* Company Name and E-mail Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Company Name</Label>
              <Input
                value={formData.companyName}
                onChange={(e) => handleInputChange("companyName", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter company name"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">E-mail</Label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter email address"
              />
            </div>
          </div>

          {/* Address1 and App Mode Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Address1</Label>
              <Input
                value={formData.address1}
                onChange={(e) => handleInputChange("address1", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter address line 1"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">App Mode</Label>
              <select
                value={formData.appMode}
                onChange={(e) => handleInputChange("appMode", e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="">Select App Mode</option>
                <option value="retail">Retail</option>
                <option value="wholesale">Wholesale</option>
                <option value="both">Both</option>
              </select>
            </div>
          </div>

          {/* Address2 and Theater PLU Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Address2</Label>
              <Input
                value={formData.address2}
                onChange={(e) => handleInputChange("address2", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter address line 2"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Theater PLU</Label>
              <Input
                value={formData.theaterPLU}
                onChange={(e) => handleInputChange("theaterPLU", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter theater PLU"
              />
            </div>
          </div>

          {/* Phone and Theater Time Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Phone</Label>
              <Input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Theater Time</Label>
              <Input
                value={formData.theaterTime}
                onChange={(e) => handleInputChange("theaterTime", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter theater time"
              />
            </div>
          </div>

          {/* Tax% and Deli Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-bold text-gray-700 mb-2 block">Tax%</Label>
              <Input
                type="number"
                step="0.001"
                value={formData.taxPercent}
                onChange={(e) => handleInputChange("taxPercent", e.target.value)}
                className="border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter tax percentage"
              />
            </div>
            <div className="flex items-center space-x-2 pt-8">
              <Checkbox
                id="deli"
                checked={formData.deli}
                onCheckedChange={(checked) => handleInputChange("deli", checked as boolean)}
                className="border-gray-300"
              />
              <Label htmlFor="deli" className="text-sm font-bold text-gray-700">
                Deli
              </Label>
            </div>
          </div>
        </div>
      </div>

      {/* Location Directory Table */}
      <div className="mt-8 bg-white border border-gray-300 rounded-lg shadow-lg">
        <div className="p-6">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-blue-600 text-white">
                  <th className="text-left p-3 font-bold border-r border-blue-500">Location Code</th>
                  <th className="text-left p-3 font-bold border-r border-blue-500">Location</th>
                  <th className="text-left p-3 font-bold border-r border-blue-500">Company Name</th>
                  <th className="text-left p-3 font-bold border-r border-blue-500">Address1</th>
                  <th className="text-left p-3 font-bold border-r border-blue-500">Address2</th>
                  <th className="text-center p-3 font-bold border-r border-blue-500">Phone#</th>
                  <th className="text-center p-3 font-bold">Tax%</th>
                </tr>
              </thead>
              <tbody>
                {locations.map((location, index) => (
                  <tr
                    key={location.locationCode}
                    className={`border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${
                      index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                    }`}
                  >
                    <td className="p-3 text-gray-800 font-medium border-r border-gray-200">
                      {location.locationCode}
                    </td>
                    <td className="p-3 text-gray-800 border-r border-gray-200">
                      {location.location}
                    </td>
                    <td className="p-3 text-gray-800 border-r border-gray-200">
                      {location.companyName}
                    </td>
                    <td className="p-3 text-gray-800 border-r border-gray-200">
                      {location.address1}
                    </td>
                    <td className="p-3 text-gray-800 border-r border-gray-200">
                      {location.address2}
                    </td>
                    <td className="p-3 text-gray-800 text-center border-r border-gray-200">
                      {location.phone}
                    </td>
                    <td className="p-3 text-gray-800 text-center">
                      {location.taxPercent}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
